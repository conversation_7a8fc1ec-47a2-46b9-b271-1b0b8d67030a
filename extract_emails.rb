#!/usr/bin/env ruby

require 'csv'

# Configuration
CSV_FILE_PATH = 'onboarded-users-2 (1).csv'
OUTPUT_FILE = 'extracted_emails.csv'
INTERVAL = 75 # Extract every 75th email

def extract_emails
  unless File.exist?(CSV_FILE_PATH)
    puts "Error: CSV file not found at #{CSV_FILE_PATH}"
    puts 'Please check the file path and try again.'
    exit 1
  end

  extracted_emails = []
  email_count = 0

  begin
    # Read the CSV file
    CSV.foreach(CSV_FILE_PATH, headers: true) do |row|
      # Extract every 75th email (starting from the 75th)
      if email_count % INTERVAL == 0
        # Try to find email column - common column names
        email = row['EMAIL']

        if email
          extracted_emails << email
          puts "Extracted email ##{email_count}: #{email}"
        else
          puts "Warning: No email found in row #{email_count}"
        end
      end

      email_count += 1
    end

    # Write extracted emails to output file
    File.open(OUTPUT_FILE, 'w') do |file|
      extracted_emails.each_with_index do |email, index|
        file.puts "#{email}"
      end
    end

    puts "\n" + ('=' * 50)
    puts 'Extraction completed!'
    puts "Total rows processed: #{email_count}"
    puts "Emails extracted: #{extracted_emails.length}"
    puts "Output saved to: #{OUTPUT_FILE}"
    puts '=' * 50
  rescue CSV::MalformedCSVError => e
    puts "Error: Malformed CSV file - #{e.message}"
    exit 1
  rescue StandardError => e
    puts "Error: #{e.message}"
    exit 1
  end
end

# Main execution
if ARGV.include?('--headers') || ARGV.include?('-h')
  show_csv_headers
else
  puts 'Email Extraction Script'
  puts '======================'
  puts "CSV File: #{CSV_FILE_PATH}"
  puts "Extracting every #{INTERVAL}th email..."
  puts

  extract_emails
end
