#!/usr/bin/env ruby

require 'csv'

# Configuration
CSV_FILE_PATH = '/Users/<USER>/Downloads/onboarded-users-2 (1).csv'
OUTPUT_FILE = 'extracted_emails.txt'
INTERVAL = 75  # Extract every 75th email

def extract_emails
  unless File.exist?(CSV_FILE_PATH)
    puts "Error: CSV file not found at #{CSV_FILE_PATH}"
    puts "Please check the file path and try again."
    exit 1
  end

  extracted_emails = []
  email_count = 0
  
  begin
    # Read the CSV file
    CSV.foreach(CSV_FILE_PATH, headers: true) do |row|
      email_count += 1
      
      # Extract every 75th email (starting from the 75th)
      if email_count % INTERVAL == 0
        # Try to find email column - common column names
        email = find_email_in_row(row)
        
        if email
          extracted_emails << email
          puts "Extracted email ##{email_count}: #{email}"
        else
          puts "Warning: No email found in row #{email_count}"
        end
      end
    end
    
    # Write extracted emails to output file
    File.open(OUTPUT_FILE, 'w') do |file|
      extracted_emails.each_with_index do |email, index|
        file.puts "#{index + 1}. #{email}"
      end
    end
    
    puts "\n" + "="*50
    puts "Extraction completed!"
    puts "Total rows processed: #{email_count}"
    puts "Emails extracted: #{extracted_emails.length}"
    puts "Output saved to: #{OUTPUT_FILE}"
    puts "="*50
    
  rescue CSV::MalformedCSVError => e
    puts "Error: Malformed CSV file - #{e.message}"
    exit 1
  rescue StandardError => e
    puts "Error: #{e.message}"
    exit 1
  end
end

def find_email_in_row(row)
  # Common email column names to check
  email_columns = ['email', 'Email', 'EMAIL', 'email_address', 'Email Address', 'user_email', 'contact_email']
  
  # First, try exact column name matches
  email_columns.each do |col_name|
    return row[col_name] if row[col_name] && !row[col_name].strip.empty?
  end
  
  # If no exact match, look for columns containing 'email'
  row.headers.each do |header|
    if header&.downcase&.include?('email')
      email = row[header]
      return email if email && !email.strip.empty?
    end
  end
  
  # If still no match, look for email pattern in all columns
  row.each do |field|
    next unless field
    if field.match?(/\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i)
      return field
    end
  end
  
  nil
end

def show_csv_headers
  return unless File.exist?(CSV_FILE_PATH)
  
  begin
    CSV.open(CSV_FILE_PATH, headers: true) do |csv|
      headers = csv.first&.headers
      if headers
        puts "Available columns in the CSV file:"
        headers.each_with_index do |header, index|
          puts "  #{index + 1}. #{header}"
        end
        puts
      end
    end
  rescue StandardError => e
    puts "Error reading CSV headers: #{e.message}"
  end
end

# Main execution
if ARGV.include?('--headers') || ARGV.include?('-h')
  show_csv_headers
else
  puts "Email Extraction Script"
  puts "======================"
  puts "CSV File: #{CSV_FILE_PATH}"
  puts "Extracting every #{INTERVAL}th email..."
  puts
  
  extract_emails
end
