GIT
  remote: https://github.com/ineverov/mockit.git
  revision: 6d1cdbb1e727be37ab84abdcd45e69206849b11f
  specs:
    mockit (0.1.0)
      rails (>= 6.0)
      redis
      request_store

GEM
  remote: https://gems.contribsys.com/
  specs:
    sidekiq-pro (8.0.1)
      sidekiq (>= 8.0.0, < 9)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    amazing_print (1.8.1)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    ast (2.4.3)
    async (2.25.0)
      console (~> 1.29)
      fiber-annotation
      io-event (~> 1.11)
      metrics (~> 0.12)
      traces (~> 0.15)
    aws-eventstream (1.4.0)
    aws-partitions (1.1124.0)
    aws-sdk-core (3.226.2)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.106.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.192.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    axe-matchers (2.6.1)
      dumb_delegator (~> 0.8)
      virtus (~> 1.0)
    axiom-types (0.1.1)
      descendants_tracker (~> 0.0.4)
      ice_nine (~> 0.11.0)
      thread_safe (~> 0.3, >= 0.3.1)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    better_html (2.1.1)
      actionview (>= 6.0)
      activesupport (>= 6.0)
      ast (~> 2.0)
      erubi (~> 1.4)
      parser (>= 2.4)
      smart_properties
    bigdecimal (3.2.2)
    bindex (0.8.1)
    blueprinter (1.1.2)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    bundler-audit (0.9.2)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    byebug (12.0.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    childprocess (5.0.0)
    choice (0.2.0)
    cmdparse (3.0.7)
    coderay (1.1.3)
    coercible (1.0.0)
      descendants_tracker (~> 0.0.1)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    console (1.31.0)
      fiber-annotation
      fiber-local (~> 1.1)
      json
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    cronex (0.15.0)
      tzinfo
      unicode (>= *******)
    csv (3.3.2)
    datadog (2.18.0)
      datadog-ruby_core_source (~> 3.4, >= 3.4.1)
      libdatadog (~> ********.0)
      libddwaf (~> ********.0)
      logger
      msgpack
    datadog-ruby_core_source (3.4.1)
    date (3.4.1)
    date_validator (0.12.0)
      activemodel (>= 3)
      activesupport (>= 3)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    deepsort (0.5.0)
    descendants_tracker (0.0.4)
      thread_safe (~> 0.3, >= 0.3.1)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.6.2)
    docile (1.4.0)
    docusign_esign (5.3.0)
      addressable (~> 2.7, >= 2.7.0)
      json (~> 2.1, >= 2.1.0)
      jwt (~> 2.2, >= 2.2.1)
      typhoeus (~> 1.0, >= 1.0.1)
    dogstatsd-ruby (5.7.0)
    dotenv (3.1.8)
    dotenv-rails (3.1.8)
      dotenv (= 3.1.8)
      railties (>= 6.1)
    drb (2.2.3)
    dry-cli (1.2.0)
    dumb_delegator (0.8.1)
    ejs (1.1.1)
    equalizer (0.0.11)
    erb (5.0.1)
    erb_lint (0.9.0)
      activesupport
      better_html (>= 2.0.1)
      parser (>= *******)
      rainbow
      rubocop (>= 1)
      smart_properties
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    ethon (0.16.0)
      ffi (>= 1.15.0)
    execjs (2.10.0)
    factory_bot (6.5.4)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.5.0)
      factory_bot (~> 6.5)
      railties (>= 6.1.0)
    faker (3.5.2)
      i18n (>= 1.8.11, < 2)
    faraday (2.13.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-mashify (1.0.0)
      faraday (~> 2.0)
      hashie
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.1)
      net-http (>= 0.5.0)
    faraday-retry (2.3.2)
      faraday (~> 2.0)
    ffi (1.17.2)
    ffi (1.17.2-aarch64-linux-gnu)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    fiber-annotation (0.2.0)
    fiber-local (1.1.0)
      fiber-storage
    fiber-storage (1.0.1)
    flipper (1.3.5)
      concurrent-ruby (< 2)
    flipper-notifications (0.1.7)
      activesupport (>= 7, < 8.1)
      flipper (>= 0.24, < 2.0)
      httparty (~> 0.17)
    flipper-redis (1.3.5)
      flipper (~> 1.3.5)
      redis (>= 3.0, < 6)
    flipper-ui (1.3.5)
      erubi (>= 1.0.0, < 2.0.0)
      flipper (~> 1.3.5)
      rack (>= 1.4, < 4)
      rack-protection (>= 1.5.3, < 5.0.0)
      rack-session (>= 1.0.2, < 3.0.0)
      sanitize (< 8)
    formatador (1.1.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    geom2d (0.4.1)
    git (4.0.4)
      activesupport (>= 5.0)
      addressable (~> 2.8)
      process_executer (~> 4.0)
      rchardet (~> 1.9)
    gli (2.22.2)
      ostruct
    globalid (1.2.1)
      activesupport (>= 6.1)
    guard (2.19.1)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      logger (~> 1.6)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      ostruct (~> 0.6)
      pry (>= 0.13.0)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-compat (1.2.1)
    guard-rspec (4.7.3)
      guard (~> 2.1)
      guard-compat (~> 1.1)
      rspec (>= 2.99.0, < 4.0)
    hashdiff (1.1.2)
    hashie (5.0.0)
    hexapdf (1.3.0)
      cmdparse (~> 3.0, >= 3.0.3)
      geom2d (~> 0.4, >= 0.4.1)
      openssl (>= 2.2.1)
      strscan (>= 3.1.2)
    holidays (8.8.0)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    ice_nine (0.11.2)
    io-console (0.8.0)
    io-event (1.11.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.2)
    json (2.12.2)
    json-schema (5.0.1)
      addressable (~> 2.8)
    jwt (2.10.2)
      base64
    language_server-protocol (********)
    launchy (3.0.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    letter_opener_web (3.0.0)
      actionmailer (>= 6.1)
      letter_opener (~> 1.9)
      railties (>= 6.1)
      rexml
    libdatadog (********.0)
    libdatadog (********.0-aarch64-linux)
    libdatadog (********.0-x86_64-linux)
    libddwaf (********.0)
      ffi (~> 1.0)
    libddwaf (********.0-aarch64-linux)
      ffi (~> 1.0)
    libddwaf (********.0-arm64-darwin)
      ffi (~> 1.0)
    libddwaf (********.0-x86_64-linux)
      ffi (~> 1.0)
    lint_roller (1.1.0)
    liquid (5.8.7)
      bigdecimal
      strscan (>= 3.1.1)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    logstop (0.4.1)
      logger
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lumberjack (1.2.10)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    memory_profiler (1.1.0)
    method_source (1.1.0)
    metrics (0.12.2)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    msgpack (1.8.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mustermann (3.0.3)
      ruby2_keywords (~> 0.0.1)
    mutex_m (0.3.0)
    nenv (0.3.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    openssl (3.3.0)
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    parallel (1.27.0)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pghero (3.7.0)
      activerecord (>= 7.1)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    process_executer (4.0.0)
      track_open_instances (~> 0.1)
    pry (0.15.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.11.0)
      byebug (~> 12.0)
      pry (>= 0.13, < 0.16)
    pry-rails (0.3.11)
      pry (>= 0.13.0)
    pry-remote (0.1.8)
      pry (~> 0.9)
      slop (~> 3.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-cors (3.0.0)
      logger
      rack (>= 3.0.14)
    rack-protection (4.1.1)
      base64 (>= 0.1.0)
      logger (>= 1.6.0)
      rack (>= 3.0.0, < 4)
    rack-proxy (0.7.7)
      rack
    rack-sanitizer (2.0.4)
      rack (>= 1.0, < 4.0)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-erd (1.7.2)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
      choice (~> 0.2.0)
      ruby-graphviz (~> 1.2)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-pg-extras (5.6.12)
      rails
      ruby-pg-extras (= 5.6.12)
    rails_semantic_logger (4.17.0)
      rack
      railties (>= 5.1)
      semantic_logger (~> 4.16)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rchardet (1.9.0)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.25.0)
      connection_pool
    redis-cluster-client (0.11.0)
      redis-client (~> 0.22)
    redis-clustering (5.4.0)
      redis (= 5.4.0)
      redis-cluster-client (>= 0.10.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.4.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.4)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (8.0.1)
      actionpack (>= 7.2)
      activesupport (>= 7.2)
      railties (>= 7.2)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-sidekiq (5.1.0)
      rspec-core (~> 3.0)
      rspec-expectations (~> 3.0)
      rspec-mocks (~> 3.0)
      sidekiq (>= 5, < 9)
    rspec-support (3.13.4)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rswag-api (2.16.0)
      activesupport (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    rswag-specs (2.16.0)
      activesupport (>= 5.2, < 8.1)
      json-schema (>= 2.2, < 6.0)
      railties (>= 5.2, < 8.1)
      rspec-core (>= 2.14)
    rswag-ui (2.16.0)
      actionpack (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    rubocop (1.78.0)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.45.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= *******)
      prism (~> 1.4)
    ruby-graphviz (1.2.5)
      rexml
    ruby-pg-extras (5.6.12)
      pg
      terminal-table
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    ruby_http_client (3.5.5)
    rubyzip (2.4.1)
    sanitize (7.0.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.16.8)
    scenic (1.9.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    securerandom (0.4.1)
    seed_migration (1.2.3)
    selenium-webdriver (4.34.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    semantic_logger (4.16.0)
      concurrent-ruby (~> 1.0)
    sendgrid-actionmailer (3.2.0)
      mail (~> 2.7)
      sendgrid-ruby (~> 6.4)
    sendgrid-ruby (6.7.0)
      ruby_http_client (~> 3.4)
    shellany (0.0.1)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    sidekiq (8.0.5)
      connection_pool (>= 2.5.0)
      json (>= 2.9.0)
      logger (>= 1.6.2)
      rack (>= 3.1.0)
      redis-client (>= 0.23.2)
    sidekiq-cron (2.3.0)
      cronex (>= 0.13.0)
      fugit (~> 1.8, >= 1.11.1)
      globalid (>= 1.0.1)
      sidekiq (>= 6.5.0)
    sidekiq-unique-jobs (8.0.11)
      concurrent-ruby (~> 1.0, >= 1.0.5)
      sidekiq (>= 7.0.0, < 9.0.0)
      thor (>= 1.0, < 3.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    sinatra (4.1.1)
      logger (>= 1.6.0)
      mustermann (~> 3.0)
      rack (>= 3.0.0, < 4)
      rack-protection (= 4.1.1)
      rack-session (>= 2.0.0, < 3)
      tilt (~> 2.0)
    slack-ruby-client (2.6.0)
      faraday (>= 2.0)
      faraday-mashify
      faraday-multipart
      gli
      hashie
      logger
    slop (3.6.0)
    smart_properties (1.17.0)
    smarter_csv (1.14.4)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    strscan (3.1.5)
    terminal-table (4.0.0)
      unicode-display_width (>= 1.1.1, < 4)
    thor (1.3.2)
    thread_safe (0.3.6)
    tilt (2.4.0)
    timeout (0.4.3)
    traces (0.15.2)
    track_open_instances (0.1.15)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode (*******)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    valid_email2 (7.0.13)
      activemodel (>= 6.0)
      mail (~> 2.5)
    view_component (3.23.2)
      activesupport (>= 5.2.0, < 8.1)
      concurrent-ruby (~> 1)
      method_source (~> 1.0)
    virtus (1.0.5)
      axiom-types (~> 0.1)
      coercible (~> 1.0)
      descendants_tracker (~> 0.0, >= 0.0.3)
      equalizer (~> 0.0, >= 0.0.9)
    vite_plugin_legacy (3.0.2)
      vite_ruby (~> 3.0, >= 3.0.4)
    vite_rails (3.0.19)
      railties (>= 5.1, < 9)
      vite_ruby (~> 3.0, >= 3.2.2)
    vite_ruby (3.9.1)
      dry-cli (>= 0.7, < 2)
      logger (~> 1.6)
      mutex_m
      rack-proxy (~> 0.6, >= 0.6.1)
      zeitwerk (~> 2.2)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket (1.2.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wicked_pdf (2.8.2)
      activesupport
      ostruct
    wkhtmltopdf-binary (********)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux
  arm64-darwin-22
  ruby
  x86_64-linux

DEPENDENCIES
  amazing_print
  annotate
  async (~> 2.25)
  aws-sdk-s3 (~> 1.192)
  axe-matchers
  blueprinter (~> 1.1.2)
  bootsnap
  brakeman
  bundler-audit (~> 0.9.2)
  capybara (~> 3.40)
  datadog
  date_validator (~> 0.12.0)
  debug
  deepsort (~> 0.5)
  devise (~> 4.8)
  docusign_esign (~> 5.3)
  dogstatsd-ruby
  dotenv-rails
  ejs (~> 1.1)
  erb_lint
  execjs (~> 2.10.0)
  factory_bot_rails
  faker
  faraday (~> 2.13)
  faraday-multipart
  faraday-retry
  flipper
  flipper-notifications
  flipper-redis
  flipper-ui
  git
  guard
  guard-rspec
  hexapdf
  holidays (~> 8.8)
  letter_opener_web (~> 3.0)
  liquid (~> 5.8.7)
  logstop
  memory_profiler
  mockit!
  nokogiri
  pg (~> 1.5)
  pghero
  pry-byebug
  pry-rails
  pry-remote
  puma (~> 6.6)
  rack-cors
  rack-sanitizer
  rails (~> 7.2.2)
  rails-controller-testing
  rails-erd
  rails-pg-extras
  rails_semantic_logger
  redis (~> 5.4)
  redis-clustering (~> 5.0)
  rspec-rails
  rspec-sidekiq
  rspec_junit_formatter
  rswag-api
  rswag-specs
  rswag-ui
  rubocop
  rubyzip
  scenic (~> 1.9)
  seed_migration
  selenium-webdriver
  sendgrid-actionmailer
  sendgrid-ruby
  shoulda-matchers
  sidekiq
  sidekiq-cron
  sidekiq-pro!
  sidekiq-unique-jobs
  simplecov
  sinatra
  slack-ruby-client
  smarter_csv (~> 1.14)
  sprockets-rails
  stimulus-rails
  turbo-rails
  tzinfo-data
  valid_email2 (~> 7.0)
  view_component
  vite_plugin_legacy
  vite_rails
  web-console
  webmock
  wicked_pdf (~> 2.8)
  wkhtmltopdf-binary

RUBY VERSION
   ruby 3.3.7p123

BUNDLED WITH
   2.4.19
