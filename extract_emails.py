#!/usr/bin/env python3

import csv
import re
import os
import sys

# Configuration
CSV_FILE_PATH = '/Users/<USER>/Downloads/onboarded-users-2 (1).csv'
OUTPUT_FILE = 'extracted_emails.txt'
INTERVAL = 75  # Extract every 75th email

def find_email_in_row(row):
    """Find email in a CSV row by checking common column names and patterns."""
    # Common email column names to check
    email_columns = ['email', 'Email', 'EMAIL', 'email_address', 'Email Address', 
                    'user_email', 'contact_email']
    
    # First, try exact column name matches
    for col_name in email_columns:
        if col_name in row and row[col_name] and row[col_name].strip():
            return row[col_name].strip()
    
    # If no exact match, look for columns containing 'email'
    for header in row.keys():
        if header and 'email' in header.lower():
            email = row[header]
            if email and email.strip():
                return email.strip()
    
    # If still no match, look for email pattern in all columns
    email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
    for value in row.values():
        if value and email_pattern.match(str(value)):
            return str(value).strip()
    
    return None

def show_csv_headers():
    """Display the headers of the CSV file."""
    if not os.path.exists(CSV_FILE_PATH):
        print(f"Error: CSV file not found at {CSV_FILE_PATH}")
        return
    
    try:
        with open(CSV_FILE_PATH, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            headers = reader.fieldnames
            if headers:
                print("Available columns in the CSV file:")
                for i, header in enumerate(headers, 1):
                    print(f"  {i}. {header}")
                print()
    except Exception as e:
        print(f"Error reading CSV headers: {e}")

def extract_emails():
    """Extract every 75th email from the CSV file."""
    if not os.path.exists(CSV_FILE_PATH):
        print(f"Error: CSV file not found at {CSV_FILE_PATH}")
        print("Please check the file path and try again.")
        sys.exit(1)
    
    extracted_emails = []
    email_count = 0
    
    try:
        with open(CSV_FILE_PATH, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                email_count += 1
                
                # Extract every 75th email (starting from the 75th)
                if email_count % INTERVAL == 0:
                    email = find_email_in_row(row)
                    
                    if email:
                        extracted_emails.append(email)
                        print(f"Extracted email #{email_count}: {email}")
                    else:
                        print(f"Warning: No email found in row {email_count}")
        
        # Write extracted emails to output file
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as file:
            for i, email in enumerate(extracted_emails, 1):
                file.write(f"{i}. {email}\n")
        
        print("\n" + "="*50)
        print("Extraction completed!")
        print(f"Total rows processed: {email_count}")
        print(f"Emails extracted: {len(extracted_emails)}")
        print(f"Output saved to: {OUTPUT_FILE}")
        print("="*50)
        
    except csv.Error as e:
        print(f"Error: Malformed CSV file - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

def main():
    """Main function to run the script."""
    if '--headers' in sys.argv or '-h' in sys.argv:
        show_csv_headers()
    else:
        print("Email Extraction Script")
        print("======================")
        print(f"CSV File: {CSV_FILE_PATH}")
        print(f"Extracting every {INTERVAL}th email...")
        print()
        
        extract_emails()

if __name__ == "__main__":
    main()
