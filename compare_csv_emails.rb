#!/usr/bin/env ruby

require 'csv'

# Configuration
CSV_FILE_1 = 'extracted_emails.csv'
CSV_FILE_2 = 'communication service (3).csv'
OUTPUT_DIFF_FILE = 'email_differences.csv'

def find_email_column(csv_file)
  'Find the email column in a CSV file'
  return nil unless File.exist?(csv_file)

  # Common email column names to check
  email_columns = ['email', 'Email', 'EMAIL', 'email_address', 'Email Address',
                   'user_email', 'contact_email', 'Email_Address']

  begin
    CSV.open(csv_file, headers: true) do |csv|
      headers = csv.first&.headers
      return nil unless headers

      # First, try exact column name matches
      email_columns.each do |col_name|
        return col_name if headers.include?(col_name)
      end

      # If no exact match, look for columns containing 'email'
      headers.each do |header|
        return header if header&.downcase&.include?('email')
      end
    end
  rescue StandardError => e
    puts "Error reading CSV headers from #{csv_file}: #{e.message}"
    return nil
  end

  nil
end

def extract_emails_from_csv(csv_file, email_column)
  'Extract all emails from a CSV file'
  emails = Set.new

  begin
    CSV.foreach(csv_file, headers: true) do |row|
      email = row[email_column]
      if email && !email.strip.empty?
        emails.add(email.strip.downcase) # Normalize to lowercase for comparison
      end
    end
  rescue StandardError => e
    puts "Error reading emails from #{csv_file}: #{e.message}"
    return Set.new
  end

  emails
end

def show_csv_info(csv_file)
  'Show information about a CSV file'
  return unless File.exist?(csv_file)

  begin
    row_count = 0
    headers = nil

    CSV.foreach(csv_file, headers: true) do |row|
      headers = row.headers if row_count == 0
      row_count += 1
    end

    puts "File: #{csv_file}"
    puts "  Rows: #{row_count}"
    puts "  Columns: #{headers&.length || 0}"
    puts "  Headers: #{headers.join(', ')}" if headers
    puts
  rescue StandardError => e
    puts "Error reading #{csv_file}: #{e.message}"
  end
end

def compare_emails
  'Main function to compare emails between two CSV files'

  # Check if files exist
  unless File.exist?(CSV_FILE_1)
    puts "Error: CSV file not found at #{CSV_FILE_1}"
    exit 1
  end

  unless File.exist?(CSV_FILE_2)
    puts "Error: CSV file not found at #{CSV_FILE_2}"
    exit 1
  end

  puts 'CSV Email Comparison Tool'
  puts '========================='
  puts

  # Show file information
  show_csv_info(CSV_FILE_1)
  show_csv_info(CSV_FILE_2)

  # Find email columns
  email_col_1 = find_email_column(CSV_FILE_1)
  email_col_2 = find_email_column(CSV_FILE_2)

  unless email_col_1
    puts "Error: Could not find email column in #{CSV_FILE_1}"
    exit 1
  end

  unless email_col_2
    puts "Error: Could not find email column in #{CSV_FILE_2}"
    exit 1
  end

  puts 'Email columns found:'
  puts "  #{CSV_FILE_1}: '#{email_col_1}'"
  puts "  #{CSV_FILE_2}: '#{email_col_2}'"
  puts

  # Extract emails from both files
  puts 'Extracting emails...'
  emails_1 = extract_emails_from_csv(CSV_FILE_1, email_col_1)
  emails_2 = extract_emails_from_csv(CSV_FILE_2, email_col_2)

  puts 'Emails extracted:'
  puts "  #{CSV_FILE_1}: #{emails_1.size} unique emails"
  puts "  #{CSV_FILE_2}: #{emails_2.size} unique emails"
  puts

  # Calculate differences
  only_in_file_1 = emails_1 - emails_2
  only_in_file_2 = emails_2 - emails_1
  common_emails = emails_1 & emails_2

  # Display results
  puts 'Comparison Results:'
  puts '==================='
  puts "Common emails: #{common_emails.size}"
  puts "Only in #{CSV_FILE_1}: #{only_in_file_1.size}"
  puts "Only in #{CSV_FILE_2}: #{only_in_file_2.size}"
  puts

  # Write results to CSV
  CSV.open(OUTPUT_DIFF_FILE, 'w') do |csv|
    csv << %w[Email Status Source_File]

    # Add emails only in file 1
    only_in_file_1.each do |email|
      csv << [email, 'Only in File 1', CSV_FILE_1]
    end

    # Add emails only in file 2
    only_in_file_2.each do |email|
      csv << [email, 'Only in File 2', CSV_FILE_2]
    end

    # Add common emails
    common_emails.each do |email|
      csv << [email, 'Common', 'Both Files']
    end
  end

  puts "Detailed results saved to: #{OUTPUT_DIFF_FILE}"
  puts

  # Show sample differences if any
  if only_in_file_1.any?
    puts "Sample emails only in #{CSV_FILE_1} (first 5):"
    only_in_file_1.first(5).each { |email| puts "  - #{email}" }
    puts
  end

  if only_in_file_2.any?
    puts "Sample emails only in #{CSV_FILE_2} (first 5):"
    only_in_file_2.first(5).each { |email| puts "  - #{email}" }
    puts
  end

  puts '=' * 50
end

# Main execution
if ARGV.include?('--help') || ARGV.include?('-h')
  puts 'CSV Email Comparison Tool'
  puts '========================='
  puts 'This script compares emails between two CSV files and shows the differences.'
  puts
  puts 'Files to compare:'
  puts "  File 1: #{CSV_FILE_1}"
  puts "  File 2: #{CSV_FILE_2}"
  puts
  puts "Output: #{OUTPUT_DIFF_FILE}"
  puts
  puts 'Usage:'
  puts "  ruby #{__FILE__}        # Run comparison"
  puts "  ruby #{__FILE__} --help # Show this help"
else
  compare_emails
end
