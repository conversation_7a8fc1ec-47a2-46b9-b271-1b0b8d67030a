# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Utils API', type: :request do
  describe '#import_eligibility_files' do
    it 'runs EligibilityFileImporter::Coordinator<PERSON><PERSON>' do
      post('/api/utils/import_eligibility_files')
      expect(EligibilityFileImporter::CoordinatorJob.jobs.size).to eq(1)
    end
  end

  describe '#cache_clear' do
    it 'runs Rails.cache.clear' do
      expect(Rails.cache).to receive(:clear)
      post('/api/utils/cache_clear')
    end
  end

  describe 'ams requests' do
    path '/api/utils/deliver_noaa' do
      post 'Delivers NOAA to customers' do
        include_context 'document example'
        include_context 'request with authentication'

        tags 'Loans', 'Utils', 'NOAA'
        consumes 'application/json'
        produces 'application/json'

        parameter name: :params, in: :body, schema: {
          type: :object,
          properties: {
            product_type: {
              type: :string,
              example: 'IPL',
              enum: %w[IPL UPL],
              required: true,
              description: 'Product type'
            },
            loan_id: {
              type: :string,
              example: '72350b78-7e28-404a-a54f-466f46e72d12',
              description: 'ID of the loan for which we want to trigger NOAA, required if product_type is IPL'
            },
            loan_inquiry_id: {
              type: :string,
              example: '22b64315-b738-4e48-bd12-3618897189d4',
              description: 'ID of the loan inquiry for which we want to trigger NOAA, required if product_type is UPL'
            }
          }
        }

        let!(:loan) { create(:loan) }
        let(:job_id) { 'job_id' }

        before do
          allow(Loans::DeliverNoticeOfAdverseActionJob).to receive(:perform_async).and_return(job_id)
          allow(Upl::DeliverNoticeOfAdverseActionJob).to receive(:perform_async).and_return(job_id)
        end

        response '200', 'delivers NOAA for a given loan/loan-inquiry ID' do
          let(:params) { { loan_id: loan.id, product_type: 'IPL' } }
          let(:response_body) do
            { 'message' => "IPL NOAA generation job successfully submitted. Job ID: #{job_id}" }
          end

          run_test! do |response|
            expect(response.json_body).to match(response_body)
            expect_request_event_record
          end
        end

        response '400', 'when the product type is not of IPL or UPL' do
          let(:params) { { loan_id: 'dummy_loan_id', product_type: 'non_existing_product_type' } }
          let(:response_body) do
            { 'statusCode' => 400,
              'error' => 'Bad Request',
              'message' => "Product type should be one of IPL or UPL, input product type: #{params[:product_type]}" }
          end

          run_test! do |response|
            expect(response.json_body).to match(response_body)
            expect_request_event_record
          end
        end

        response '404', 'when loan or a loan inquiry record is not found' do
          let(:params) { { loan_id: 'dummy_loan_id', product_type: 'IPL' } }
          let(:response_body) do
            { 'statusCode' => 404,
              'error' => 'Not Found',
              'message' => "Loan not found for given loan ID: #{params[:loan_id]}" }
          end

          run_test! do |response|
            expect(response.json_body).to match(response_body)
            expect_request_event_record
          end
        end
      end
    end

    path '/api/utils/sync_loan_status' do
      post 'Sync loan application status to GDS for one ore more loans' do
        include_context 'document example'
        include_context 'request with authentication'

        tags 'Loans', 'Utils'
        consumes 'application/json'
        produces 'application/json'

        parameter name: :params, in: :body, schema: {
          type: :object,
          properties: {
            loan_ids: {
              type: :array,
              example: ['fa4cc403-6b39-4be4-92e7-000777551ba2'],
              description: 'Loan ids specifying loans to be synced'
            },
            request_ids: {
              type: :array,
              example: ['658370c11a1ad1155914ffd7'],
              description: 'Request ids specifying loans to be synced'
            },
            unified_ids: {
              type: :array,
              example: ['43287932'],
              description: 'Unified ids specifying loans to be synced'
            }
          }
        }

        let!(:loan) { create(:loan) }

        before do
          allow(Loans::SyncStatusJob).to receive(:perform_async).with(loan.id)
        end

        response '200', 'sync status job enqueued' do
          let(:params) do
            {
              loan_ids: [],
              request_ids: [],
              unified_ids: [loan.unified_id]
            }
          end
          let(:response_body) do
            { 'sync_status_jobs_enqueued' => 1 }
          end

          run_test! do |response|
            expect(response.json_body).to match(response_body)
            expect_request_event_record
          end
        end
      end
    end

    path '/api/utils/force_loan_onboard' do
      post 'Forces loan onboarding to dash or loanpro for a given unified id' do
        include_context 'document example'
        include_context 'request with authentication'

        tags 'Loans', 'Utils'
        consumes 'application/json'
        produces 'application/json'

        parameter name: :params, in: :body, schema: {
          type: :object,
          properties: {
            to_entity: {
              type: :string,
              example: 'dash',
              description: 'onboard to "dash" or to "loanpro"',
              enum: %w[dash loanpro]
            },
            unified_id: {
              type: :string,
              example: '43287932',
              description: 'Unified ID of a loan that needs force onboarding to dash'
            }
          }
        }

        let!(:loan) { create(:loan) }

        before do
          allow(Onboarding::DashOnboarding).to receive(:call)
          allow(Onboarding::LoanproOnboarding).to receive(:call)
          create(:lead, code: loan.code)
          create(:til_history, loan:, signed_at: Time.now)
        end

        response '200', 'force onboard to dash or loanpro' do
          let(:params) { { unified_id: loan.unified_id, to_entity: %w[dash loanpro].sample } }
          let(:response_body) do
            { 'message' => "#{params[:to_entity].capitalize} onboarding successful for loan with unified id: #{loan.unified_id}" }
          end

          run_test! do |response|
            expect(response.json_body).to match(response_body)
            expect_request_event_record
          end
        end

        response '400', 'when an unknown entity name is passed an input' do
          let(:params) { { unified_id: loan.unified_id, to_entity: 'non_existing_entity' } }
          let(:response_body) do
            { 'statusCode' => 400,
              'error' => 'Bad Request',
              'message' => 'Entity name should either be one of: dash, loanpro' }
          end

          run_test! do |response|
            expect(response.json_body).to match(response_body)
            expect_request_event_record
          end
        end

        response '404', 'when loan or a signed TIL history record not found' do
          let(:params) { { unified_id: 'dummy_unified_id', to_entity: %w[dash loanpro].sample } }
          let(:response_body) do
            { 'statusCode' => 404,
              'error' => 'Not Found',
              'message' => "Loan not found for given unified id: #{params[:unified_id]}" }
          end

          run_test! do |response|
            expect(response.json_body).to match(response_body)
            expect_request_event_record
          end
        end
      end
    end

    path '/api/utils/force_socure_monitoring' do
      post 'Forces socure monitoring for a give loanpro loan id' do
        include_context 'document example'
        include_context 'request with authentication'

        tags 'Loans', 'Utils'
        consumes 'application/json'
        produces 'application/json'

        parameter name: :params, in: :body, schema: {
          type: :object,
          properties: {
            loan_id: {
              type: :string,
              example: '123',
              description: 'loan ID that needs forced socure onboarding'
            },
            operation: {
              type: :string,
              example: 'enable',
              description: 'enable/disable monitoring'
            }
          }
        }

        before do
          allow(Loans::SocureMonitoringJob).to receive(:perform_async)
        end

        response '200', 'force onboard to dash or loanpro' do
          let!(:loan) { create(:loan) }
          let(:params) { { loan_id: loan.id, operation: 'enable' } }
          let(:response_body) do
            { 'message' => "socure monitoring successful for loan id: #{loan.id}" }
          end

          run_test! do |response|
            expect(response.json_body).to match(response_body)
            expect_request_event_record
          end
        end
      end
    end

    path '/api/utils/force_plaid_asset_report' do
      post 'Forces generating plaid asset report for a give loan id' do
        include_context 'document example'
        include_context 'request with authentication'

        tags 'Loans', 'Utils'
        consumes 'application/json'
        produces 'application/json'

        parameter name: :params, in: :body, schema: {
          type: :object,
          properties: {
            loan_id: {
              type: :string,
              example: '123',
              description: 'loan ID that needs forced socure onboarding'
            }
          }
        }
        response '200', 'force generating plaid asset report' do
          let!(:loan) { create(:loan) }
          let(:params) { { loan_id: loan.id } }
          let(:response_body) do
            { 'message' => "force_plaid_asset_report successful for loan id: #{loan.id}" }
          end

          run_test! do |response|
            expect(response.json_body).to match(response_body)
            expect_request_event_record
          end
        end
      end
    end

    path '/api/utils/generate_offer_code' do
      post 'Generates a intake-page url with eligible lead code in lower environments' do
        tags 'Loans', 'Utils'
        consumes 'application/json'
        produces 'application/json'

        parameter name: :params, in: :body, schema: {
          type: :object,
          properties: {
            source: {
              type: :string,
              example: 'bf',
              description: 'Source of the lead',
              enum: %w[bf fllg]
            }
          }
        }

        let!(:loan) { create(:loan, loan_app_status_id: '14', code: 'ABC123', product_type: 'IPL') }
        let!(:lead) { create(:lead, code: 'ABC123', type: 'IPL') }
        let(:params) { { source: %w[bf fllg].sample } }

        response '200', 'returns a intake page url with eligible lead code' do
          let(:response_body) do
            { 'url' => "http://funnel-#{Rails.env}.abovelending.com/graduation-loan/intake-page-1?s=#{params[:source]}&offer=#{lead.code}" }
          end

          run_test! do |response|
            expect(response.json_body).to match(response_body)
            expect_request_event_record
          end
        end

        response '403', 'returns disabled endpoint message when attempted from production' do
          before do
            allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('production'))
          end

          let(:response_body) { { 'error' => 'This endpoint is disabled in production' } }

          run_test! do |response|
            expect(response.json_body).to match(response_body)
          end
        end

        response '404', 'returns no leads available message when no eligible leads are found' do
          before do
            Lead.destroy_all
          end

          let(:response_body) do
            {
              'error' => 'No leads available to generate offer code'
            }
          end

          run_test! do |response|
            expect(response.json_body).to match(response_body)
            expect_request_event_record
          end
        end
      end
    end

    path '/api/utils/upload_report' do
      post 'Uploads documents to Arix for a given unified id' do
        # include_context 'document example'
        include_context 'request with authentication'

        tags 'Loans', 'Utils'
        consumes 'multipart/form-data'
        produces 'application/vnd.api+json'

        parameter name: :document, in: :formData, schema: {
          type: :object,
          properties: {
            unified_id: {
              type: :string,
              example: '1234567890',
              description: 'Unified id of the loan'
            },
            report_type: {
              type: :string,
              example: 'SOCURE_REPORT',
              description: 'Document type'
            },
            file: {
              type: :file,
              format: :binary,
              description: 'Document to upload to Arix'
            }
          }
        }
        let(:document) do
          {
            unified_id:, report_type:, file:
          }
        end
        let(:file) do
          Rack::Test::UploadedFile.new(file_path, 'application/xml')
        end
        let(:file_path) { Rails.root.join('spec', 'fixtures', 'files', 'test_report.xml') }
        let!(:loan) { create(:loan) }
        let(:report_type) { 'SOCURE_REPORT' }
        let(:unified_id) { loan.unified_id }

        before do
          allow(ArixOnboarding::SubmitDocumentToArix).to receive(:call)
        end

        response '201', 'uploads document to Arix' do
          let(:params) { { unified_id:, report_type:, document: } }

          run_test! do |response|
            expect(response).to be_created

            expect(ArixOnboarding::SubmitDocumentToArix).to have_received(:call) do |args|
              expect(args[:unified_id]).to eq(loan.unified_id)
              expect(args[:report_type]).to eq('SOCURE_REPORT')
              expect(args[:file].original_filename).to eq('test_report.xml')
            end
          end
        end
      end
    end

    path '/api/utils/generate_magic_link_token/{id}' do
      get 'Generates a magic link token for a given borrower' do
        include_context 'request with authentication'

        tags 'Loans', 'Utils'
        produces 'application/vnd.api+json'

        parameter name: :id, in: :path, schema: { type: :string }, description: 'ID of borrower record'

        let(:borrower) { create(:borrower) }

        let(:id) { borrower.id }

        response '200', 'Returns magic link token' do
          run_test! do |response|
            expect(response).to be_successful
            token = JSON.parse(response.body).fetch('magic_link_token')

            expect(Borrower.find_by_token_for(:magic_link, token)).to eq(borrower)
          end
        end
      end
    end

    path '/api/utils/generate_offers/{loan_id}' do
      post 'Triggers the offer generation process for a loan whose origination process was interrupted due to maintenance mode being enabled (i.e halted after the completion of the initial application data collection forms (i.e. PI1 & PI2) but before the generation of offers for this application).' do
        include_context 'request with authentication'

        tags 'Loans', 'Utils'
        produces 'application/vnd.api+json'

        parameter name: :loan_id, in: :path, schema: { type: :string }, description: 'ID of loan record'

        let(:loan) { create(:loan, requested_offers: false) }

        let(:loan_id) { loan.id }

        before { allow(Clients::GdsApi).to receive(:get_offers).and_return({}) }

        response '200', 'Successfully Triggered' do
          context 'when experiment_2025_04_CHI_1753_Credit_Model_1_0 is NOT active' do
            run_test! do |response|
              expect(response).to be_successful
              expect(Clients::GdsApi).to have_received(:get_offers).with(request_id: loan.request_id, loan:, experiment_cohort: nil)
              expect(loan.reload.requested_offers).to eq(true)
            end
          end

          context 'when experiment_2025_04_CHI_1753_Credit_Model_1_0 is active' do
            before { Flipper.enable(:experiment_2025_04_CHI_1753_Credit_Model_1_0) }

            let(:experiment_cohort) { Experiment['2025_04_CHI_1753_Credit_Model_1_0'].cohort_for(loan.borrower) }

            run_test! do |response|
              expect(response).to be_successful
              expect(response.body).to eq({ result: 'Offer generation successfully triggered.' }.to_json)
              expect(Clients::GdsApi).to have_received(:get_offers).with(request_id: loan.request_id, loan:, experiment_cohort:)
            end
          end
        end

        response '422', 'Offers Already Generated' do
          before { loan.update(requested_offers: true) }

          run_test! do |response|
            expect(response).to be_unprocessable
            expect(response.body).to eq({ result: 'No action taken. Offers previously generated.' }.to_json)
            expect(Clients::GdsApi).not_to have_received(:get_offers)
          end
        end
      end
    end

    path '/api/utils/stamp_loan_agreement' do
      post 'Create a tamped version of the Loan Agreement' do
        include_context 'request with authentication'

        tags 'Loans', 'Utils'
        consumes 'application/json'
        produces 'application/json'

        parameter name: :params, in: :body, schema: {
          type: :object,
          properties: {
            loan_id: {
              type: :string,
              example: '441371c4-cd49-43e0-9009-b1a901391fe8',
              required: true,
              description: 'Loan ID or Unified ID'
            },
            override_file: {
              type: :boolean,
              example: false,
              description: 'Override existing stamped document'
            }
          }
        }

        let(:laonpro_loan) { create(:loanpro_loan) }
        let(:loanpro_loan_id) { laonpro_loan.loanpro_loan_id }
        let(:loan_id) { laonpro_loan.display_id }
        let(:deliver_email) { true }
        let(:override_file) { false }

        let(:params) { { loan_id:, deliver_email:, override_file: } }

        before do
          allow(Loanpro::StampContractJob).to receive(:perform_async).and_return('stamp_job_id')
          laonpro_loan.update!(offer_id: create(:offer, loan: laonpro_loan.loan, selected: true).id)
        end

        response '201', 'Performs stamp with no override' do
          run_test! do |response|
            expect(response).to be_created
            expect(response.body).to eq({ job_id: 'stamp_job_id' }.to_json)
            expect(Loanpro::StampContractJob).to have_received(:perform_async).with(loanpro_loan_id, true, false)
          end
        end

        response '201', 'Performs stamp with override' do
          let(:override_file) { true }

          run_test! do |response|
            expect(response).to be_created
            expect(response.body).to eq({ job_id: 'stamp_job_id' }.to_json)
            expect(Loanpro::StampContractJob).to have_received(:perform_async).with(loanpro_loan_id, true, true)
          end
        end

        response '201', 'Performs stamp without deliver' do
          let(:deliver_email) { false }

          run_test! do |response|
            expect(response).to be_created
            expect(response.body).to eq({ job_id: 'stamp_job_id' }.to_json)
            expect(Loanpro::StampContractJob).to have_received(:perform_async).with(loanpro_loan_id, false, false)
          end
        end

        response '201', 'Works with Loan ID' do
          let(:loan_id) { laonpro_loan.loan.id }

          run_test! do |response|
            expect(response).to be_created
            expect(response.body).to eq({ job_id: 'stamp_job_id' }.to_json)
            expect(Loanpro::StampContractJob).to have_received(:perform_async).with(loanpro_loan_id, true, false)
          end
        end

        response '201', 'Works with loanpro id' do
          let(:loan_id) { laonpro_loan.loanpro_loan_id }

          run_test! do |response|
            expect(response).to be_created
            expect(response.body).to eq({ job_id: 'stamp_job_id' }.to_json)
            expect(Loanpro::StampContractJob).to have_received(:perform_async).with(loanpro_loan_id, true, false)
          end
        end

        response '404', 'Loan not found' do
          let(:loan_id) { 'non_existing_loan_id' }

          run_test! do |response|
            expect(response).to be_not_found
            expect(response.body).to eq({ error: 'No Loan or LoanPro loan found with the specified ID' }.to_json)
          end
        end
      end
    end
  end
end
