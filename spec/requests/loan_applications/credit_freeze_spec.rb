# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanApplications::CreditFreezeController, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  before { mock_trustpilot_summary_response }

  describe 'routing' do
    it_behaves_like 'an enforced originations funnel route', { current_path: :additional_info_loan_applications_path, unauthenticated: false }
  end

  let!(:loan) { create(:ipl_loan, code: 'abc123', loan_app_status_id: LoanAppStatus::ID_TO_NAME.index('BASIC_INFO_COMPLETE'), source_type: 'WEB') }
  let!(:borrower_id) { loan.borrower_id }
  let!(:query) { { offer: 'abc123', s: 'bf' } }
  let!(:session) { { borrower_id:, code: 'abc123', service_entity: 'bf' } }

  before { mock_session!(session) }

  describe '#credit_freeze' do
    before { create(:loan_detail, loan:, credit_freeze_active: true) }

    it 'renders the credit freeze page' do
      get credit_freeze_loan_applications_path(query)

      expect(response).to render_template(:credit_freeze)

      expect_request_event_record

      expect(session[:code]).to eq('abc123')
      expect(session[:service_entity]).to eq('bf')
    end
  end

  describe '#credit_freeze_resubmit' do
    before do
      allow(Loans::GenerateOffersJob).to receive(:perform_async)
      create(:loan_detail, loan: loan, credit_freeze_active: true)
    end

    context 'when in maintenance mode' do
      before { Flipper.enable(:maintenance_mode) }

      it 'redirects to the maintenance page' do
        turbo_post path: credit_freeze_resubmit_loan_applications_path(query)

        expect(response).to redirect_to(maintenance_page_path)
      end
    end

    context 'when the borrower does not have a credit freeze' do
      before { loan.loan_detail.update(credit_freeze_active: false) }

      it 'redirects to the resume page' do
        turbo_post path: credit_freeze_resubmit_loan_applications_path(query)

        expect(response).to redirect_to(resume_borrowers_path)

        event = expect_request_event_record
        expect(event.metadata['redirected_to']).to eq(resume_borrowers_path)
      end

      it 'records metadata on submission' do
        turbo_post path: credit_freeze_resubmit_loan_applications_path(query)

        event = expect_request_event_record

        expected_metadata = {
          code: 'abc123',
          credit_freeze_active: false,
          generate_offers_job_enqueued: false,
          is_in_maintenance_mode: false
        }.as_json
        expect(event.metadata).to include(expected_metadata)
      end
    end

    context 'when the borrower has a credit freeze' do
      it 'resets the credit freeze flag and updates the loan status' do
        expect(loan.loan_detail.credit_freeze_active).to be true

        turbo_post path: credit_freeze_resubmit_loan_applications_path(query)

        expect(loan.reload.loan_app_status.name).to eq(LoanAppStatus::ADD_INFO_COMPLETE_STATUS)
        expect(loan.loan_detail.credit_freeze_active).to be false
      end

      it 'resets the offer poll timestamp' do
        session[:offer_wait_at] = Time.current
        expect(session[:offer_wait_at]).to be_within(1.second).of(Time.current)

        turbo_post path: credit_freeze_resubmit_loan_applications_path(query)

        expect(session[:offer_wait_at]).to be_nil
      end

      it 'records metadata on submission' do
        turbo_post path: credit_freeze_resubmit_loan_applications_path(query)

        event = expect_request_event_record

        expected_metadata = {
          code: 'abc123',
          credit_freeze_active: true,
          generate_offers_job_enqueued: true,
          is_in_maintenance_mode: false
        }.as_json
        expect(event.metadata).to include(expected_metadata)
      end
    end

    context 'when an error occurs' do
      before do
        credit_freeze_resubmit_service = double(call: true, meta: {})
        allow(LoanApplications::CreditFreezeResubmit).to receive(:new).and_return(credit_freeze_resubmit_service)
        allow(credit_freeze_resubmit_service).to receive(:call).and_raise(StandardError, 'Boom!')
      end

      it 'redirects to whoops page' do
        turbo_post path: credit_freeze_resubmit_loan_applications_path(query)

        expect(response).to redirect_to(whoops_exit_pages_path(offer: session[:code], s: session[:service_entity]))
      end

      it 'records metadata on submission' do
        turbo_post path: credit_freeze_resubmit_loan_applications_path(query)

        event = expect_request_event_record

        expected_metadata = {
          code: 'abc123',
          credit_freeze_active: true,
          is_in_maintenance_mode: false
        }.as_json
        expect(event.metadata).to include(expected_metadata)
      end
    end
  end
end
