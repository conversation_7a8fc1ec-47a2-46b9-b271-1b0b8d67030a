# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanApplications::ExitPagesController, type: :request do
  let(:query) { { offer: 'abc123', s: 'bf' } }

  before { mock_trustpilot_summary_response }

  describe '#active_application' do
    it 'renders' do
      get active_application_exit_pages_path(query)

      expect(response).to render_template(:active_application)

      expect_request_event_record

      expect(request.session[:code]).to eq('abc123')
      expect(request.session[:service_entity]).to eq('bf')
    end
  end

  describe '#application_processing' do
    it 'renders' do
      get application_processing_exit_pages_path(query)

      expect(response).to render_template(:application_processing)

      expect_request_event_record

      expect(request.session[:code]).to eq('abc123')
      expect(request.session[:service_entity]).to eq('bf')
    end

    it 'renders the beyond finance number' do
      get application_processing_exit_pages_path(query)

      assert_select('a[data-testid="phone"][href="tel:8002012295"]')
    end

    it 'renders the five lakes law group number' do
      get application_processing_exit_pages_path(query.merge(s: 'fllg'))

      assert_select('a[data-testid="phone"][href="tel:8009730729"]')
    end
  end

  describe '#no_offer' do
    it 'renders' do
      get no_offer_exit_pages_path(query)

      expect(response).to render_template(:no_offer)

      expect_request_event_record

      expect(request.session[:code]).to eq('abc123')
      expect(request.session[:service_entity]).to eq('bf')
    end
  end

  describe '#thank_you' do
    it 'renders' do
      mock_trustpilot_reviews_response

      get thank_you_exit_pages_path(query)

      expect(response).to render_template(:thank_you)

      expect_request_event_record

      expect(request.session[:code]).to eq('abc123')
      expect(request.session[:service_entity]).to eq('bf')
    end
  end

  describe '#whoops' do
    it 'renders' do
      get whoops_exit_pages_path(query)

      expect(response).to render_template(:whoops)

      expect_request_event_record

      expect(request.session[:code]).to eq('abc123')
      expect(request.session[:service_entity]).to eq('bf')
    end
  end
end
