# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanApplications::SelectOfferController, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  before { mock_trustpilot_summary_response }

  describe 'routing' do
    it_behaves_like 'an enforced originations funnel route', { current_path: :select_offer_loan_applications_path, unauthenticated: false }
    it_behaves_like 'a page that is disabled during maintenance mode', { current_path: :select_offer_loan_applications_path, loan_app_status: LoanAppStatus::ADD_INFO_COMPLETE_STATUS }
  end

  describe '#select_offer' do
    let(:code) { 'abc123' }
    let(:service_entity) { 'bf' }
    let(:query) { { offer: code, s: service_entity } }
    let(:loan_app_status) { LoanAppStatus.for('ADD_INFO_COMPLETE') }
    let!(:borrower) { create(:borrower) }
    let!(:loan) { create(:loan, borrower:, code:, loan_app_status:, product_type: 'IPL', requested_offers: true) }
    let(:session) { { code:, service_entity:, borrower_id: borrower.id } }

    before do
      freeze_time
      mock_session!(session)
    end

    it 'renders the select offer page' do
      get select_offer_loan_applications_path(query)

      expect(response).to render_template(:select_offer)

      expect(session[:code]).to eq('abc123')
      expect(session[:service_entity]).to eq('bf')

      expect_request_event_record
    end

    it 'renders the loading component and polling' do
      get select_offer_loan_applications_path(query)

      assert_select("[data-testid='select-offer-loading']")
    end

    it 'redirects if polling has exceeded time limit' do
      get select_offer_loan_applications_path(query)
      travel_to(61.seconds.from_now)
      get select_offer_loan_applications_path(query)

      expected_redirect = application_processing_exit_pages_path(offer: code, s: service_entity)
      expect(response).to redirect_to(expected_redirect)

      event = expect_request_event_record
      expect(event.metadata['redirected_to']).to eq(expected_redirect)
    end

    it 'redirects to no-offer if loan application has been frontend declined' do
      fed_borrower = create(:borrower)
      create(:loan, borrower: fed_borrower,
                    code: query[:offer],
                    loan_app_status: LoanAppStatus.for(LoanAppStatus::FRONT_END_DECLINED_STATUS),
                    product_type: Lead::TYPES[:IPL],
                    requested_offers: true)

      # NOTE:  Should render declined regardless of polling start
      mock_session!(borrower_id: fed_borrower.id, offer_wait_at: 2.hours.ago.iso8601)

      get select_offer_loan_applications_path(query)

      expect(response).to redirect_to(no_offer_exit_pages_path(offer: code, s: service_entity))
    end

    it 'redirects to credit freeze page if corresponding flag is set on loan details' do
      create(:loan_detail, loan:, credit_freeze_active: true)
      get select_offer_loan_applications_path(query)

      expected_redirect = credit_freeze_loan_applications_path(offer: code, s: service_entity)
      expect(response).to redirect_to(expected_redirect)

      event = expect_request_event_record
      expect(event.metadata['redirected_to']).to eq(expected_redirect)
    end

    it 'renders offer details when an offer exists' do
      offer = create(:offer, loan:)
      create(:apr_calculation,
             loan_amount: 10_000,
             sum_of_payments: 10_000,
             offer_id: offer.id,
             calculated_by: :apr_calculator)

      # NOTE:  Should render offer if available regardless of polling start
      mock_session!(borrower_id: borrower.id, offer_wait_at: 2.hours.ago.iso8601)

      get select_offer_loan_applications_path(query)

      assert_select("[data-testid='select-offer-header']")
      assert_select("[data-testid='select-offer-card']")

      expect_request_event_record
    end
  end

  describe '#select_offer_create' do
    let(:code) { 'abc123' }
    let(:service_entity) { 'bf' }
    let(:query) { { offer: code, s: service_entity } }
    let(:borrower) { create(:borrower) }
    let(:loan_app_status) { LoanAppStatus.for('ADD_INFO_COMPLETE') }
    let(:loan) { create(:loan, borrower:, code:, loan_app_status:, product_type: 'IPL', requested_offers: true) }

    let(:offer_id) { SecureRandom.uuid }
    let(:select_offer_double) { double(call: true, meta: {}) }

    let(:session) { { code:, service_entity:, borrower_id: borrower.id } }

    let(:valid_attributes) do
      {
        loan_applications_select_offer_form_model: {
          loan_id: loan.id,
          offer_id:
        }
      }
    end

    before do
      mock_session!(session)
      allow(LoanApplications::SelectOffer).to receive(:new).and_return(select_offer_double)
    end

    it 'calls the LoanApplications::SelectOffer service with correct attributes' do
      turbo_post path: select_offer_create_loan_applications_path, params: valid_attributes

      expect(LoanApplications::SelectOffer).to have_received(:new).with(hash_including(loan_id: loan.id, offer_id:))
      expect(select_offer_double).to have_received(:call)

      expect_request_event_record
    end

    context 'when the request comes from the multi-offer form' do
      let(:valid_attributes) do
        {
          loan_applications_multi_offer_option_form_model: {
            loan_id: loan.id,
            offer_id:
          }
        }
      end

      it 'calls the LoanApplications::SelectOffer service with correct attributes' do
        turbo_post path: select_offer_create_loan_applications_path, params: valid_attributes

        expect(LoanApplications::SelectOffer).to have_received(:new).with(hash_including(loan_id: loan.id, offer_id:))
        expect(select_offer_double).to have_received(:call)
      end
    end

    it 'redirects to the bank account verification documents page' do
      turbo_post path: select_offer_create_loan_applications_path, params: valid_attributes

      expect(response).to redirect_to(bank_accounts_path(offer: code, s: service_entity))

      expect_request_event_record
    end

    it 'redirects to whoops on error' do
      expect(select_offer_double).to receive(:call).and_raise(LoanApplications::SelectOffer::OfferSelectionError.new('Boom!'))

      turbo_post path: select_offer_create_loan_applications_path, params: valid_attributes

      expect(response).to redirect_to(whoops_exit_pages_path(query))
      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank

      expect_request_event_record
    end
  end
end
