# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Servicing::DashboardController, type: :request do
  include ActiveSupport::NumberHelper

  describe 'routing' do
    it_behaves_like 'an enforced originations funnel route', { current_path: :servicing_dashboard_index_path }
  end

  let(:code) { 'Wv1F5Q' }
  let!(:landing_lead) { create(:landing_lead, lead_code: code) }
  let!(:borrower) { create(:borrower, email: landing_lead.email) }
  let!(:loan) { create(:loan, :onboarded, :investor_assigned, borrower:, code:, product_type: Loan::IPL_LOAN_PRODUCT_TYPE) }
  let!(:loan_doc) { create(:doc, loan:) }
  let!(:loan_doc_name_match) { create(:doc, loan:, name: "DOCTITLE_version_0_FIRSTNAME_LASTNAME_#{SecureRandom.uuid}.pdf") }
  let(:session) { { code:, service_entity: 'bf', borrower_id: borrower.id } }
  let!(:loanpro_loan) { create(:loanpro_loan, loan:, til_sign_date: Date.today) }

  let(:payment_date) { 1.month.ago }
  let(:last_payment) do
    {
      payment_amount: 123.45,
      payment_principal: 23.45,
      payment_interest: 100,
      date: payment_date.iso8601
    }.stringify_keys
  end

  let(:dashboard_details_payload) do
    {
      'apr' => '27.1727',
      'loan_amount' => 12_388.04,
      'underwriting' => 1196.56,
      'loan_payment' => 351.09,
      'number_of_terms' => 71,
      'contract_date' => '2025-01-10',
      'current_due_date' => '2025-03-04',
      'current_payment_due' => '0.00',
      'days_past_due' => 0,
      'loan_status_text' => 'Open',
      'number_of_remaining_terms' => 71,
      'next_payment_amount' => '351.09',
      'next_payment_date' => '2025-03-04',
      'overdue_amount' => '0.00',
      'payment_frequency' => 'loan.frequency.monthly',
      'remaining_balance' => 12_902.45,
      'sub_status' => 'Good Standing',
      'sub_status_id' => 9,
      'last_payment' => last_payment,
      'borrower_name' => 'ERICA LAMBERT',
      'address' => '251 Will Circles apt 1',
      'city' => 'West Nicolefort',
      'state' => 'CA',
      'zip_code' => '94025',
      'debt_sale' => false,
      'beneficial_owner_name' => nil
    }
  end

  let(:dashboard_details) do
    Clients::DashServicingApi::DashboardDetails.new(dashboard_details_payload)
  end

  let(:dashboard_details_sold_titan) do
    Clients::DashServicingApi::DashboardDetails.new(
      dashboard_details_payload.merge(
        'debt_sale' => true,
        'beneficial_owner_name' => 'Titan Asset Purchasing'
      )
    )
  end

  let(:dashboard_details_sold_velocity) do
    Clients::DashServicingApi::DashboardDetails.new(
      dashboard_details_payload.merge(
        'debt_sale' => true,
        'beneficial_owner_name' => 'Velocity Investments, LLC'
      )
    )
  end

  let(:dashboard_details_sold_generic) do
    Clients::DashServicingApi::DashboardDetails.new(
      dashboard_details_payload.merge(
        'debt_sale' => true,
        'beneficial_owner_details' => {
          'name' => 'Owner Name',
          'company_name' => 'Owner Name, LLC',
          'address_street' => 'PO Box 1234',
          'address_apt' => nil,
          'city' => 'New York',
          'state' => 'NY',
          'zip_code' => '12345',
          'email' => '<EMAIL>',
          'phone' => '18883456789',
          'website' => 'https://www.ownername.com'
        }
      )
    )
  end

  let(:dashboard_details_sold_no_email_no_website) do
    Clients::DashServicingApi::DashboardDetails.new(
      dashboard_details_payload.merge(
        'debt_sale' => true,
        'beneficial_owner_details' => {
          'name' => 'Owner Name',
          'company_name' => 'Owner Name, LLC',
          'address_street' => 'PO Box 1234',
          'address_apt' => nil,
          'city' => 'New York',
          'state' => 'NY',
          'zip_code' => '12345',
          'phone' => '18883456789'
        }
      )
    )
  end

  let(:dashboard_details_closed) do
    Clients::DashServicingApi::DashboardDetails.new(
      dashboard_details_payload.merge(
        'loan_status_text' => 'Paid Off'
      )
    )
  end

  let(:dashboard_details_charged_off) do
    Clients::DashServicingApi::DashboardDetails.new(
      dashboard_details_payload.merge(
        'sub_status' => 'Closed - Charged Off'
      )
    )
  end

  let(:dashboard_details_past_due) do
    Clients::DashServicingApi::DashboardDetails.new(
      dashboard_details_payload.merge(
        'sub_status' => 'Past Due'
      )
    )
  end

  let(:payment_profiles) do
    payload = [
      {
        'id' => 55_865,
        'isPrimary' => 1,
        'isSecondary' => 0,
        'title' => 'Personal Account ********',
        'type' => 'paymentAccount.type.checking',
        'checkingAccountId' => 55_873,
        'active' => 1,
        'visible' => 1,
        'bankName' => 'Wells Fargo',
        'accountNumber' => '3333',
        'routingNumber' => '*********'
      },
      {
        'id' => 55_867,
        'isPrimary' => 0,
        'isSecondary' => 1,
        'title' => 'Checking Account 1231234',
        'type' => 'paymentAccount.type.checking',
        'checkingAccountId' => 55_874,
        'active' => 0,
        'visible' => 1,
        'bankName' => 'Wells Fargo',
        'accountNumber' => '55555',
        'routingNumber' => '*********'
      }
    ]

    Clients::DashServicingApi::PaymentProfiles.new(payload)
  end

  let(:upcoming_payments) do
    payload = [
      { 'id' => 283_615, 'amount' => '981.00', 'date' => '2025-03-06', 'type' => 'recurring', 'status' => 'pending' },
      { 'id' => 287_526, 'amount' => '1317.00', 'date' => '2025-03-18', 'type' => 'single', 'status' => 'pending' }
    ]

    Clients::DashServicingApi::UpcomingPayments.new(payload)
  end

  let(:payment_history) do
    payload = {
      'payments' => [
        { 'id' => 251_456,
          'amount' => '177.59',
          'date' => '2025-02-14T00:00:00+00:00',
          'type' => 'Unknown',
          'status' => 'success',
          'isCustomerInitiated' => false,
          'interest' => '118.37',
          'principal' => '59.22',
          'afterBalance' => '12952.07' },
        { 'id' => 241_967,
          'amount' => '177.59',
          'date' => '2025-01-02T00:00:00+00:00',
          'type' => 'Unknown',
          'status' => 'success',
          'isCustomerInitiated' => false,
          'interest' => '124.21',
          'principal' => '53.38',
          'afterBalance' => '13005.45' },
        { 'id' => 300_200,
          'amount' => '200.00',
          'date' => '2025-02-20T00:00:00+00:00',
          'type' => 'Unknown',
          'status' => 'voided',
          'isCustomerInitiated' => true,
          'interest' => '124.21',
          'principal' => '53.38',
          'afterBalance' => '13005.45' }
      ],
      'count' => 3,
      'loanpro_loan_id' => '144923'
    }

    Clients::DashServicingApi::PaymentHistory.new(payload)
  end

  before do
    mock_session!(session)

    allow(Users::CheckAccountActivation).to receive(:call).and_return(false)
    allow(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_return(loanpro_loan)
  end

  describe 'routing' do
    it 'redirects to dashboard page if onboarded' do
      get congratulations_contracts_path

      expect(response).to redirect_to(servicing_dashboard_index_path(offer: code, s: :bf))
    end
  end

  describe '#index' do
    before do
      allow(Loanpro::DashboardDetails).to receive(:call).and_return(dashboard_details)
      allow(Loanpro::PaymentProfiles).to receive(:call).and_return(payment_profiles)
      allow(Loanpro::UpcomingPayments).to receive(:call).and_return(upcoming_payments)
      allow(Loanpro::PaymentHistory).to receive(:call).and_return(payment_history)
    end

    it 'renders open loan' do
      get servicing_dashboard_index_path

      expect(response).to be_successful

      # Sold card
      expect(response.body).not_to include('Notification of Assignment, Sale and Transfer')
      expect(response.body).not_to include('Titan Asset Purchasing')

      # Closed card
      expect(response.body).not_to include('Your loan is closed.')

      # Charged Off card
      expect(response.body).not_to include('Your loan balance has been charged off due to non-payment')

      # Loan details card
      expect(response.body).to include(number_to_currency(dashboard_details.next_payment_amount))
      expect(response.body).to include(dashboard_details.next_payment_date.strftime('%b %d, %Y'))
      assert_select('[data-testid="make-payment-button"]', text: 'Make a Payment')
      assert_select('[data-testid="payment-type-auto-pay"]', text: 'Active')

      assert_select('[data-testid="payment-frequency"] p.mb-0', text: 'Monthly')
      assert_select('[data-testid="remaining-payments"] p.mb-0', text: dashboard_details.number_of_remaining_terms.to_s)
      assert_select('[data-testid="initial-loan-amount"] p.mb-0', text: number_to_currency(dashboard_details.initial_amount))
      assert_select('[data-testid="apr"] p.mb-0', text: "#{dashboard_details.apr.to_f.round(2)} %")
      assert_select('[data-testid="payoff-amount"] p.mb-0', text: number_to_currency(dashboard_details.payoff_amount))

      # Payment activity card
      expect(response.body).to include(number_to_currency(upcoming_payments.payments[0].amount))
      expect(response.body).to include(number_to_currency(upcoming_payments.payments[1].amount))
      expect(response.body).to include(number_to_currency(payment_history.payments[1].amount))
      expect(response.body).to include(number_to_currency(payment_history.payments[1].amount))

      # Documents card
      expect(response.body).to include(loan_doc.name)
      expect(response.body).to include(loan_doc.id)
      expect(response.body).to include('DOCTITLE.pdf')
      expect(response.body).to include(loan_doc_name_match.id)

      # Contact card
      expect(response.body).not_to include(dashboard_details.address)
      expect(response.body).not_to include(dashboard_details.city)

      # Payment buttons visible
      expect(response.body).to include('Make a Payment')
    end

    it 'renders charged off loan' do
      allow(Loanpro::DashboardDetails).to receive(:call).and_return(dashboard_details_charged_off)

      get servicing_dashboard_index_path

      expect(response).to be_successful

      # Sold card
      expect(response.body).not_to include('Notification of Assignment, Sale and Transfer')
      expect(response.body).not_to include('Titan Asset Purchasing')

      # Closed card
      expect(response.body).not_to include('Your loan is closed.')

      # Charged Off card
      expect(response.body).to include('Your loan balance has been charged off due to non-payment')

      # Past due card
      expect(response.body).not_to include('Loan Past Due')

      # Loan details card
      expect(response.body).not_to include(number_to_currency(dashboard_details.next_payment_amount))
      expect(response.body).to include(dashboard_details.next_payment_date.strftime('%b %d, %Y'))
      assert_select('[data-testid="make-payment-button"]', text: 'Make a Payment')
      assert_select('[data-testid="payment-type-auto-pay"]', text: 'Active')

      assert_select('[data-testid="payment-frequency"] p.mb-0', text: 'Monthly')
      assert_select('[data-testid="remaining-payments"] p.mb-0', text: dashboard_details.number_of_remaining_terms.to_s, count: 0)
      assert_select('[data-testid="initial-loan-amount"] p.mb-0', text: number_to_currency(dashboard_details.initial_amount))
      assert_select('[data-testid="apr"] p.mb-0', text: "#{dashboard_details.apr.to_f.round(2)} %")
      assert_select('[data-testid="payoff-amount"] p.mb-0', text: number_to_currency(dashboard_details.payoff_amount))

      # Payment activity card
      expect(response.body).to include(number_to_currency(upcoming_payments.payments[0].amount))
      expect(response.body).to include(number_to_currency(upcoming_payments.payments[1].amount))
      expect(response.body).to include(number_to_currency(payment_history.payments[1].amount))
      expect(response.body).to include(number_to_currency(payment_history.payments[1].amount))

      # Documents card
      expect(response.body).to include(loan_doc.name)
      expect(response.body).to include(loan_doc.id)
      expect(response.body).to include('DOCTITLE.pdf')
      expect(response.body).to include(loan_doc_name_match.id)

      # Contact card
      expect(response.body).not_to include(dashboard_details.address)
      expect(response.body).not_to include(dashboard_details.city)
    end

    it 'renders sold loan to Titan' do
      allow(Loanpro::DashboardDetails).to receive(:call).and_return(dashboard_details_sold_titan)

      get servicing_dashboard_index_path

      expect(response).to be_successful

      # Sold card
      expect(response.body).to include('Notification of Assignment, Sale and Transfer')
      expect(response.body).to include('Titan Asset Purchasing')

      # Closed card
      expect(response.body).not_to include('Your loan is closed.')

      # Charged Off card
      expect(response.body).not_to include('Your loan balance has been charged off due to non-payment')

      # Past due card
      expect(response.body).not_to include('Loan Past Due')

      # Loan details card
      expect(response.body).not_to include(number_to_currency(dashboard_details_sold_titan.next_payment_amount))
      expect(response.body).not_to include(dashboard_details_sold_titan.next_payment_date.strftime('%b %d, %Y'))
      assert_select('[data-testid="make-payment-button"]', text: 'Make a Payment', count: 0)
      assert_select('[data-testid="payment-type-auto-pay"]', text: 'Active', count: 0)

      assert_select('[data-testid="payment-frequency"] p.mb-0', text: 'Monthly', count: 0)
      assert_select('[data-testid="remaining-payments"] p.mb-0', text: dashboard_details.number_of_remaining_terms.to_s, count: 0)
      assert_select('[data-testid="initial-loan-amount"] p.mb-0', text: number_to_currency(dashboard_details.initial_amount), count: 0)
      assert_select('[data-testid="apr"] p.mb-0', text: "#{dashboard_details.apr.to_f.round(2)} %", count: 0)
      assert_select('[data-testid="payoff-amount"] p.mb-0', text: number_to_currency(dashboard_details.payoff_amount), count: 0)

      # Payment activity card
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[0].amount))
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[1].amount))
      expect(response.body).not_to include(number_to_currency(payment_history.payments[1].amount))
      expect(response.body).not_to include(number_to_currency(payment_history.payments[1].amount))

      # Documents card
      expect(response.body).not_to include(loan_doc.name)
      expect(response.body).not_to include(loan_doc.id)
      expect(response.body).not_to include('DOCTITLE.pdf')
      expect(response.body).not_to include(loan_doc_name_match.id)

      # Contact card
      expect(response.body).not_to include(dashboard_details_sold_titan.address)
      expect(response.body).not_to include(dashboard_details_sold_titan.city)

      # Payment buttons not visible
      expect(response.body).not_to include('Make a Payment')
    end

    it 'renders sold loan to Velocity' do
      allow(Loanpro::DashboardDetails).to receive(:call).and_return(dashboard_details_sold_velocity)

      get servicing_dashboard_index_path

      expect(response).to be_successful

      # Sold card
      expect(response.body).to include('Notification of Assignment, Sale and Transfer')
      expect(response.body).to include('Velocity Investments, LLC')

      # Closed card
      expect(response.body).not_to include('Your loan is closed.')

      # Charged Off card
      expect(response.body).not_to include('Your loan balance has been charged off due to non-payment')

      # Past due card
      expect(response.body).not_to include('Loan Past Due')

      # Loan details card
      expect(response.body).not_to include(number_to_currency(dashboard_details_sold_velocity.next_payment_amount))
      expect(response.body).not_to include(dashboard_details_sold_velocity.next_payment_date.strftime('%b %d, %Y'))
      assert_select('[data-testid="make-payment-button"]', text: 'Make a Payment', count: 0)
      assert_select('[data-testid="payment-type-auto-pay"]', text: 'Active', count: 0)

      assert_select('[data-testid="payment-frequency"] p.mb-0', text: 'Monthly', count: 0)
      assert_select('[data-testid="remaining-payments"] p.mb-0', text: dashboard_details.number_of_remaining_terms.to_s, count: 0)
      assert_select('[data-testid="initial-loan-amount"] p.mb-0', text: number_to_currency(dashboard_details.initial_amount), count: 0)
      assert_select('[data-testid="apr"] p.mb-0', text: "#{dashboard_details.apr.to_f.round(2)} %", count: 0)
      assert_select('[data-testid="payoff-amount"] p.mb-0', text: number_to_currency(dashboard_details.payoff_amount), count: 0)

      # Payment activity card
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[0].amount))
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[1].amount))
      expect(response.body).not_to include(number_to_currency(payment_history.payments[1].amount))
      expect(response.body).not_to include(number_to_currency(payment_history.payments[1].amount))

      # Documents card
      expect(response.body).not_to include(loan_doc.name)
      expect(response.body).not_to include(loan_doc.id)
      expect(response.body).not_to include('DOCTITLE.pdf')
      expect(response.body).not_to include(loan_doc_name_match.id)

      # Contact card
      expect(response.body).not_to include(dashboard_details_sold_velocity.address)
      expect(response.body).not_to include(dashboard_details_sold_velocity.city)

      # Payment buttons not visible
      expect(response.body).not_to include('Make a Payment')
    end

    it 'renders sold loan to any owner' do
      allow(Loanpro::DashboardDetails).to receive(:call).and_return(dashboard_details_sold_generic)
      get servicing_dashboard_index_path

      expect(response).to be_successful

      # Sold card
      expect(response.body).to include('Notification of Assignment, Sale and Transfer')
      expect(response.body).to include('Owner Name, LLC')
      expect(response.body).to include('Email:')
      expect(response.body).to include('Website:')

      # Closed card
      expect(response.body).not_to include('Your loan is closed.')

      # Charged Off card
      expect(response.body).not_to include('Your loan balance has been charged off due to non-payment')

      # Past due card
      expect(response.body).not_to include('Loan Past Due')

      # Loan details card
      expect(response.body).not_to include(number_to_currency(dashboard_details_sold_velocity.next_payment_amount))
      expect(response.body).not_to include(dashboard_details_sold_velocity.next_payment_date.strftime('%b %d, %Y'))
      assert_select('[data-testid="make-payment-button"]', text: 'Make a Payment', count: 0)
      assert_select('[data-testid="payment-type-auto-pay"]', text: 'Active', count: 0)

      assert_select('[data-testid="payment-frequency"] p.mb-0', text: 'Monthly', count: 0)
      assert_select('[data-testid="remaining-payments"] p.mb-0', text: dashboard_details.number_of_remaining_terms.to_s, count: 0)
      assert_select('[data-testid="initial-loan-amount"] p.mb-0', text: number_to_currency(dashboard_details.initial_amount), count: 0)
      assert_select('[data-testid="apr"] p.mb-0', text: "#{dashboard_details.apr.to_f.round(2)} %", count: 0)
      assert_select('[data-testid="payoff-amount"] p.mb-0', text: number_to_currency(dashboard_details.payoff_amount), count: 0)

      # Payment activity card
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[0].amount))
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[1].amount))
      expect(response.body).not_to include(number_to_currency(payment_history.payments[1].amount))
      expect(response.body).not_to include(number_to_currency(payment_history.payments[1].amount))

      # Documents card
      expect(response.body).not_to include(loan_doc.name)
      expect(response.body).not_to include(loan_doc.id)
      expect(response.body).not_to include('DOCTITLE.pdf')
      expect(response.body).not_to include(loan_doc_name_match.id)

      # Contact card
      expect(response.body).not_to include(dashboard_details_sold_velocity.address)
      expect(response.body).not_to include(dashboard_details_sold_velocity.city)

      # Payment buttons not visible
      expect(response.body).not_to include('Make a Payment')
    end

    it 'renders sold loan to an owner without email/website' do
      allow(Loanpro::DashboardDetails).to receive(:call).and_return(dashboard_details_sold_no_email_no_website)

      get servicing_dashboard_index_path

      expect(response).to be_successful

      # Sold card
      expect(response.body).to include('Notification of Assignment, Sale and Transfer')
      expect(response.body).to include('Owner Name, LLC')
      expect(response.body).not_to include('Email:')
      expect(response.body).not_to include('Website:')

      # Closed card
      expect(response.body).not_to include('Your loan is closed.')

      # Charged Off card
      expect(response.body).not_to include('Your loan balance has been charged off due to non-payment')

      # Past due card
      expect(response.body).not_to include('Loan Past Due')

      # Loan details card
      expect(response.body).not_to include(number_to_currency(dashboard_details_sold_velocity.next_payment_amount))
      expect(response.body).not_to include(dashboard_details_sold_velocity.next_payment_date.strftime('%b %d, %Y'))
      assert_select('[data-testid="make-payment-button"]', text: 'Make a Payment', count: 0)
      assert_select('[data-testid="payment-type-auto-pay"]', text: 'Active', count: 0)

      assert_select('[data-testid="payment-frequency"] p.mb-0', text: 'Monthly', count: 0)
      assert_select('[data-testid="remaining-payments"] p.mb-0', text: dashboard_details.number_of_remaining_terms.to_s, count: 0)
      assert_select('[data-testid="initial-loan-amount"] p.mb-0', text: number_to_currency(dashboard_details.initial_amount), count: 0)
      assert_select('[data-testid="apr"] p.mb-0', text: "#{dashboard_details.apr.to_f.round(2)} %", count: 0)
      assert_select('[data-testid="payoff-amount"] p.mb-0', text: number_to_currency(dashboard_details.payoff_amount), count: 0)

      # Payment activity card
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[0].amount))
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[1].amount))
      expect(response.body).not_to include(number_to_currency(payment_history.payments[1].amount))
      expect(response.body).not_to include(number_to_currency(payment_history.payments[1].amount))

      # Documents card
      expect(response.body).not_to include(loan_doc.name)
      expect(response.body).not_to include(loan_doc.id)
      expect(response.body).not_to include('DOCTITLE.pdf')
      expect(response.body).not_to include(loan_doc_name_match.id)

      # Contact card
      expect(response.body).not_to include(dashboard_details_sold_velocity.address)
      expect(response.body).not_to include(dashboard_details_sold_velocity.city)

      # Payment buttons not visible
      expect(response.body).not_to include('Make a Payment')
    end

    it 'renders closed loan' do
      allow(Loanpro::DashboardDetails).to receive(:call).and_return(dashboard_details_closed)

      get servicing_dashboard_index_path

      expect(response).to be_successful

      # Sold card
      expect(response.body).not_to include('Notification of Assignment, Sale and Transfer')
      expect(response.body).not_to include('Titan Asset Purchasing')

      # Closed card
      expect(response.body).to include('Your loan is closed.')

      # Charged Off card
      expect(response.body).not_to include('Your loan balance has been charged off due to non-payment')

      # Past due card
      expect(response.body).not_to include('Loan Past Due')

      # Loan details card
      expect(response.body).not_to include(number_to_currency(dashboard_details_sold_titan.next_payment_amount))
      expect(response.body).not_to include(dashboard_details_sold_titan.next_payment_date.strftime('%b %d, %Y'))
      assert_select('[data-testid="make-payment-button"]', text: 'Make a Payment', count: 0)
      assert_select('[data-testid="payment-type-auto-pay"]', text: 'Active', count: 0)

      assert_select('[data-testid="payment-frequency"] p.mb-0', text: 'Monthly', count: 0)
      assert_select('[data-testid="remaining-payments"] p.mb-0', text: dashboard_details.number_of_remaining_terms.to_s, count: 0)
      assert_select('[data-testid="initial-loan-amount"] p.mb-0', text: number_to_currency(dashboard_details.initial_amount), count: 0)
      assert_select('[data-testid="apr"] p.mb-0', text: "#{dashboard_details.apr.to_f.round(2)} %", count: 0)
      assert_select('[data-testid="payoff-amount"] p.mb-0', text: number_to_currency(dashboard_details.payoff_amount), count: 0)

      # Payment activity card
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[0].amount))
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[1].amount))
      expect(response.body).not_to include(number_to_currency(payment_history.payments[1].amount))
      expect(response.body).not_to include(number_to_currency(payment_history.payments[1].amount))

      # Documents card
      expect(response.body).to include(loan_doc.name)
      expect(response.body).to include(loan_doc.id)
      expect(response.body).to include('DOCTITLE.pdf')
      expect(response.body).to include(loan_doc_name_match.id)

      # Contact card
      expect(response.body).not_to include(dashboard_details_sold_titan.address)
      expect(response.body).not_to include(dashboard_details_sold_titan.city)

      # Payment buttons not visible
      expect(response.body).not_to include('Make a Payment')
    end

    it 'renders past due loan' do
      allow(Loanpro::DashboardDetails).to receive(:call).and_return(dashboard_details_past_due)

      get servicing_dashboard_index_path

      expect(response).to be_successful

      # Sold card
      expect(response.body).not_to include('Notification of Assignment, Sale and Transfer')
      expect(response.body).not_to include('Titan Asset Purchasing')

      # Closed card
      expect(response.body).not_to include('Your loan is closed.')

      # Charged Off card
      expect(response.body).not_to include('Your loan balance has been charged off due to non-payment')

      # Past due card
      expect(response.body).to include('Loan Past Due')

      # Loan details card
      expect(response.body).to include(number_to_currency(dashboard_details.next_payment_amount))
      expect(response.body).to include(dashboard_details.next_payment_date.strftime('%b %d, %Y'))
      assert_select('[data-testid="make-payment-button"]', text: 'Make a Payment')
      assert_select('[data-testid="payment-type-auto-pay"]', text: 'Active')

      assert_select('[data-testid="payment-frequency"] p.mb-0', text: 'Monthly')
      assert_select('[data-testid="remaining-payments"] p.mb-0', text: dashboard_details.number_of_remaining_terms.to_s)
      assert_select('[data-testid="initial-loan-amount"] p.mb-0', text: number_to_currency(dashboard_details.initial_amount))
      assert_select('[data-testid="apr"] p.mb-0', text: "#{dashboard_details.apr.to_f.round(2)} %")
      assert_select('[data-testid="payoff-amount"] p.mb-0', text: number_to_currency(dashboard_details.payoff_amount))

      # Payment activity card
      expect(response.body).to include(number_to_currency(upcoming_payments.payments[0].amount))
      expect(response.body).to include(number_to_currency(upcoming_payments.payments[1].amount))
      expect(response.body).to include(number_to_currency(payment_history.payments[1].amount))
      expect(response.body).to include(number_to_currency(payment_history.payments[1].amount))

      # Documents card
      expect(response.body).to include(loan_doc.name)
      expect(response.body).to include(loan_doc.id)
      expect(response.body).to include('DOCTITLE.pdf')
      expect(response.body).to include(loan_doc_name_match.id)

      # Contact card
      expect(response.body).not_to include(dashboard_details.address)
      expect(response.body).not_to include(dashboard_details.city)

      # Payment buttons visible
      expect(response.body).to include('Make a Payment')
    end

    it 'renders with dash upcoming payments error' do
      allow(Loanpro::UpcomingPayments).to receive(:call).and_raise(Clients::DashServicingApi::Error.new('Boom!'))

      get servicing_dashboard_index_path

      expect(response).to be_successful

      # Sold card
      expect(response.body).not_to include('Notification of Assignment, Sale and Transfer')
      expect(response.body).not_to include('Titan Asset Purchasing')

      # Closed card
      expect(response.body).not_to include('Your loan is closed.')

      # Charged Off card
      expect(response.body).not_to include('Your loan balance has been charged off due to non-payment')

      # Loan details card
      expect(response.body).to include(number_to_currency(dashboard_details.next_payment_amount))
      expect(response.body).to include(dashboard_details.next_payment_date.strftime('%b %d, %Y'))
      assert_select('[data-testid="make-payment-button"]', text: 'Make a Payment')
      assert_select('[data-testid="payment-type-manual"]', text: 'Inactive')

      assert_select('[data-testid="payment-frequency"] p.mb-0', text: 'Monthly')
      assert_select('[data-testid="remaining-payments"] p.mb-0', text: dashboard_details.number_of_remaining_terms.to_s)
      assert_select('[data-testid="initial-loan-amount"] p.mb-0', text: number_to_currency(dashboard_details.initial_amount))
      assert_select('[data-testid="apr"] p.mb-0', text: "#{dashboard_details.apr.to_f.round(2)} %")
      assert_select('[data-testid="payoff-amount"] p.mb-0', text: number_to_currency(dashboard_details.payoff_amount))

      # Payment activity card
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[0].amount))
      expect(response.body).not_to include(number_to_currency(upcoming_payments.payments[1].amount))
      expect(response.body).to include(number_to_currency(payment_history.payments[1].amount))
      expect(response.body).to include(number_to_currency(payment_history.payments[1].amount))

      # Documents card
      expect(response.body).to include(loan_doc.name)
      expect(response.body).to include(loan_doc.id)
      expect(response.body).to include('DOCTITLE.pdf')
      expect(response.body).to include(loan_doc_name_match.id)

      # Contact card
      expect(response.body).not_to include(dashboard_details.address)
      expect(response.body).not_to include(dashboard_details.city)
    end

    it 'handles error message for LoanproLoan error and records metadata' do
      allow(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_raise(StandardError.new('Boom!'))

      get servicing_dashboard_index_path

      expect(response).to be_successful
      event = expect_request_event_record('get_servicing_dashboard_index')
      expect(event.metadata['api_loanpro_loan_error']).to eq('Boom!')
    end

    it 'redirects to the whoops page if an unexpected error occurs' do
      allow(Loanpro::DashboardDetails).to receive(:call).and_raise('Boom!')

      get servicing_dashboard_index_path

      expected_redirect = whoops_servicing_dashboard_index_path(offer: session[:code], s: session[:service_entity])
      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank

      expect(response).to redirect_to(expected_redirect)

      expect_request_event_record('get_servicing_dashboard_index')
    end

    it 'logs whoops metadata on error' do
      allow(Loanpro::DashboardDetails).to receive(:call).and_raise('Boom!')

      get servicing_dashboard_index_path
      follow_redirect!

      event = expect_request_event_record('get_servicing_dashboard_whoops')

      expect(event.metadata['has_whoops_data']).to be_truthy
      expect(event.metadata['error_message']).to eq('Boom!')
      expect(event.metadata['referrer_request_id']).not_to be_blank
    end

    it 'logs the referrer metadata on whoops error' do
      allow(Loanpro::DashboardDetails).to receive(:call).and_raise('Boom!')

      get servicing_dashboard_index_path('get_servicing_dashboard_index')

      # simulate redirect to test with a referrer
      get whoops_servicing_dashboard_index_path(offer: session[:code], s: session[:service_entity]), headers: { 'HTTP_REFERER' => 'http://example.com/first' }

      event = expect_request_event_record('get_servicing_dashboard_whoops')
      expect(event.metadata['referrer']).to eq('http://example.com/first')
    end

    context 'when servicing_loanpro_loan_from_api Flipper is enabled', with_feature_flag: :servicing_loanpro_loan_from_api do
      it 'is successful' do
        get servicing_dashboard_index_path

        expect(response).to be_successful
      end

      it 'uses the loanpro loans loanpro_loans record' do
        loanpro_loan2 = create(:loanpro_loan)

        expect(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_return(loanpro_loan2)
        allow(Loanpro::PaymentHistory)
          .to receive(:call)
          .with(loan_id: loanpro_loan2.loanpro_loan_id)
          .and_return(payment_history)

        get servicing_dashboard_index_path

        expect(response).to be_successful
      end
    end
  end
end
