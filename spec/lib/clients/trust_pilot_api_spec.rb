# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Clients::TrustPilotApi do
  let(:memory_store) { ActiveSupport::Cache.lookup_store(:memory_store) }
  let(:test_access_token) { 'abcd1234xyz987==' }
  let(:auth_cache_key) { "trust_pilot_access_token/#{trust_pilot_config[:account_id]}/#{trust_pilot_config[:account_password].last(5)}" }
  let(:trust_pilot_config) { Rails.application.config_for(:trust_pilot_api) }

  before do
    allow(Rails).to receive(:cache).and_return(memory_store)
    Rails.cache.clear
  end

  shared_examples 'a cached authentication operation' do
    it 'uses a cached access token when one is available in the cache' do
      Rails.cache.write(auth_cache_key, test_access_token)

      auth_request = stub_request(:post, "#{trust_pilot_config[:auth_base_url]}/accesstoken")

      expected_headers = {
        'Authorization' => "Bearer #{test_access_token}",
        'Content-Type' => 'application/json'
      }
      target_action_request = success_stub_request.with(headers: expected_headers)

      subject

      expect(auth_request).not_to have_been_requested
      expect(target_action_request).to have_been_requested
    end

    it 'generates a new access token and caches it when none is available in the cache' do
      auth_url = "#{trust_pilot_config[:auth_base_url]}/accesstoken"
      auth_request = stub_request(:post, auth_url).to_return(body: { access_token: test_access_token }.to_json, headers: { 'Content-Type': 'application/json' })

      expected_headers = {
        'Authorization' => "Bearer #{test_access_token}",
        'Content-Type' => 'application/json'
      }
      target_action_request = success_stub_request.with(headers: expected_headers)

      subject

      expect(auth_request).to have_been_requested
      expect(target_action_request).to have_been_requested

      cached_token = Rails.cache.read(auth_cache_key)
      expect(cached_token).to eq(test_access_token)
    end
  end

  describe '#build_invitation' do
    let(:unified_id) { Faker::Number.number(digits: 8) }
    let(:borrower_email) { Faker::Internet.email }
    let(:borrower_full_name) { Faker::Name.name }

    subject { Clients::TrustPilotApi.new.build_invitation(unified_id, borrower_email, borrower_full_name) }

    it 'creates an appropriate invitation payload' do
      expect(subject).to eq(
        {
          locale: trust_pilot_config[:locale],
          replyTo: trust_pilot_config[:reply_to],
          senderName: 'Above Lending',
          senderEmail: trust_pilot_config[:sender_email],
          serviceReviewInvitation: {
            redirectUri: trust_pilot_config[:redirect_uri],
            templateId: trust_pilot_config[:ipl_template_id]
          },
          consumerName: borrower_full_name,
          consumerEmail: borrower_email,
          unifiedId: unified_id
        }
      )
    end
  end

  describe '#reviews' do
    let(:reviews_url) do
      "#{trust_pilot_config[:business_units_public_url]}/#{trust_pilot_config[:business_unit_id]}/reviews"
    end
    let(:success_stub_request) do
      stub_request(:get, reviews_url)
        .to_return(
          status: 200,
          headers: { 'Content-Type': 'application/json' },
          body: { links: [], reviews: [] }.to_json
        )
    end

    let(:failed_stub_request) do
      stub_request(:get, reviews_url)
        .to_return(
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          body: { error_type: 'INVALID_REQUEST', error_code: 'MISSING_FIELDS' }.to_json
        )
    end

    it 'retrieves and caches review data' do
      reviews_request = success_stub_request
                        .with(query: {
                                apikey: trust_pilot_config[:api_key],
                                page: 1,
                                perPage: 3,
                                orderBy: 'createdat.desc',
                                stars: 5
                              })

      expect(Clients::TrustPilotApi.new.reviews).to eq({ 'links' => [], 'reviews' => [] })
      expect(Clients::TrustPilotApi.new.reviews).to eq({ 'links' => [], 'reviews' => [] })

      expect(reviews_request).to have_been_requested.once
    end

    it 'raises a client error when request fails' do
      failed_stub_request
        .with(query: {
                apikey: trust_pilot_config[:api_key],
                page: 1,
                perPage: 3,
                orderBy: 'createdat.desc',
                stars: 5
              })

      expect { Clients::TrustPilotApi.new.reviews }.to raise_error(Clients::TrustPilotApi::Error) do |error|
        expect(error.wrapped_exception).to be_a(Faraday::BadRequestError)
        expect(error.message).to match(
          response_body: { 'error_type' => 'INVALID_REQUEST', 'error_code' => 'MISSING_FIELDS' },
          response_status: 400,
          klass: 'Faraday::BadRequestError',
          message: /the server responded with status 400/
        )
      end
    end
  end

  describe '#send_invitation' do
    subject { Clients::TrustPilotApi.new.send_invitation(invitation) }

    let(:invitation) do
      {
        locale: 'en-US',
        replyTo: '<EMAIL>',
        senderName: 'Above Lending',
        senderEmail: '<EMAIL>',
        serviceReviewInvitation: {
          redirectUri: 'https://abovelending.com',
          templateId: 'abcd1234'
        },
        consumerName: 'John Doe',
        consumerEmail: '<EMAIL>'
      }
    end
    let(:send_invitation_url) do
      "#{trust_pilot_config[:invitation_base_url]}/#{trust_pilot_config[:business_unit_id]}/email-invitations"
    end
    let(:success_stub_request) do
      stub_request(:post, send_invitation_url).to_return(status: 202, body: '')
    end

    let(:failed_stub_request) do
      stub_request(:post, send_invitation_url)
        .to_return(
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          body: { error_type: 'INVALID_REQUEST', error_code: 'MISSING_FIELDS' }.to_json
        )
    end

    it_behaves_like 'a cached authentication operation'

    context 'with a cached auth token' do
      before do
        Rails.cache.write(auth_cache_key, test_access_token)
      end

      it 'triggers an invitation to be sent via the Trust Pilot API' do
        create_envelope_request = success_stub_request.with(body: invitation.to_json)
        subject
        expect(create_envelope_request).to have_been_requested
      end

      it 'raises a client error when request fails' do
        failed_stub_request.with(body: invitation.to_json)

        expect { subject }.to raise_error(Clients::TrustPilotApi::Error) do |error|
          expect(error.wrapped_exception).to be_a(Faraday::BadRequestError)
          expect(error.message).to match(
            response_body: { 'error_type' => 'INVALID_REQUEST', 'error_code' => 'MISSING_FIELDS' },
            response_status: 400,
            klass: 'Faraday::BadRequestError',
            message: /the server responded with status 400/
          )
        end
      end
    end
  end

  describe '#score_string' do
    let(:score_string_url) { 'https://api.trustpilot.com/v1/resources/strings/stars/5' }

    let(:summary_success_stub_request) do
      summary_url = "#{trust_pilot_config[:business_units_public_url]}/find"
      stub_request(:get, summary_url).with(
        query: {
          apikey: trust_pilot_config[:api_key],
          name: 'abovelending.com'
        }
      )
    end

    let(:score_string_success_stub_request) do
      stub_request(:get, score_string_url)
        .to_return(
          status: 200,
          headers: { 'Content-Type': 'application/json' },
          body: { locale: 'en-US', string: 'Excellent', stars: 5 }.to_json
        )
    end

    let(:failed_score_string_stub_request) do
      stub_request(:get, score_string_url)
        .to_return(
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          body: { error_type: 'INVALID_REQUEST', error_code: 'MISSING_FIELDS' }.to_json
        )
    end

    it 'parses summary data for score string url and fetches score string data' do
      summary_request = summary_success_stub_request.to_return(
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        body: {
          links: [
            { rel: Clients::TrustPilotApi::STAR_RESOURCE_IDENTIFER, href: score_string_url },
            { rel: 'another-resource', href: 'https://another-url' }
          ]
        }.to_json
      )

      score_string_request = score_string_success_stub_request.with(
        query: {
          apikey: trust_pilot_config[:api_key]
        }
      )

      expect(Clients::TrustPilotApi.new.score_string).to eq({ 'locale' => 'en-US', 'string' => 'Excellent', 'stars' => 5 })
      expect(Clients::TrustPilotApi.new.score_string).to eq({ 'locale' => 'en-US', 'string' => 'Excellent', 'stars' => 5 })

      expect(summary_request).to have_been_requested.once
      expect(score_string_request).to have_been_requested.once
    end

    it 'returns an empty response if unable to find score string url in summary response' do
      summary_request = summary_success_stub_request.to_return(
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        body: {
          links: [
            { rel: 'another-resource', href: 'https://another-url' }
          ]
        }.to_json
      )

      expect(Clients::TrustPilotApi.new.score_string).to eq({})
      expect(Clients::TrustPilotApi.new.score_string).to eq({})

      expect(summary_request).to have_been_requested.once
    end

    it 'raises a client error when request fails' do
      summary_success_stub_request.to_return(
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        body: {
          links: [
            { rel: Clients::TrustPilotApi::STAR_RESOURCE_IDENTIFER, href: score_string_url },
            { rel: 'another-resource', href: 'https://another-url' }
          ]
        }.to_json
      )

      score_string_request = failed_score_string_stub_request.with(
        query: {
          apikey: trust_pilot_config[:api_key]
        }
      )

      expect { Clients::TrustPilotApi.new.score_string }.to raise_error(Clients::TrustPilotApi::Error) do |error|
        expect(error.wrapped_exception).to be_a(Faraday::BadRequestError)
        expect(error.message).to match(
          response_body: { 'error_type' => 'INVALID_REQUEST', 'error_code' => 'MISSING_FIELDS' },
          response_status: 400,
          klass: 'Faraday::BadRequestError',
          message: /the server responded with status 400/
        )
      end

      expect(score_string_request).to have_been_requested.once
    end
  end

  describe '#summary' do
    let(:summary_url) { "#{trust_pilot_config[:business_units_public_url]}/find" }
    let(:success_stub_request) do
      stub_request(:get, summary_url)
        .to_return(
          status: 200,
          headers: { 'Content-Type': 'application/json' },
          body: { id: '5f610fd7873ddf0001500e1b' }.to_json
        )
    end

    let(:failed_stub_request) do
      stub_request(:get, summary_url)
        .to_return(
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          body: { error_type: 'INVALID_REQUEST', error_code: 'MISSING_FIELDS' }.to_json
        )
    end

    it 'retrieves and caches review data' do
      summary_request = success_stub_request.with(
        query: {
          apikey: trust_pilot_config[:api_key],
          name: 'abovelending.com'
        }
      )

      expect(Clients::TrustPilotApi.new.summary).to eq({ 'id' => '5f610fd7873ddf0001500e1b' })
      expect(Clients::TrustPilotApi.new.summary).to eq({ 'id' => '5f610fd7873ddf0001500e1b' })

      expect(summary_request).to have_been_requested.once
    end

    it 'raises a client error when request fails' do
      summary_request = failed_stub_request.with(
        query: {
          apikey: trust_pilot_config[:api_key],
          name: 'abovelending.com'
        }
      )

      expect { Clients::TrustPilotApi.new.summary }.to raise_error(Clients::TrustPilotApi::Error) do |error|
        expect(error.wrapped_exception).to be_a(Faraday::BadRequestError)
        expect(error.message).to match(
          response_body: { 'error_type' => 'INVALID_REQUEST', 'error_code' => 'MISSING_FIELDS' },
          response_status: 400,
          klass: 'Faraday::BadRequestError',
          message: /the server responded with status 400/
        )
      end

      expect(summary_request).to have_been_requested.once
    end
  end
end
