# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Clients::TalkdeskApi do
  let(:memory_store) { ActiveSupport::Cache.lookup_store(:memory_store) }
  let(:test_access_token) { 'abcd1234xyz987==' }
  let(:auth_cache_key) { "talkdesk_access_token/#{talkdesk_config[:client_id]}/#{talkdesk_config[:client_secret].last(5)}" }
  let(:talkdesk_config) { Rails.application.config_for(:talkdesk_api) }

  before do
    allow(Rails).to receive(:cache).and_return(memory_store)
    Rails.cache.clear
  end

  shared_examples 'a cached authentication operation' do
    it 'uses a cached access token when one is available in the cache' do
      Rails.cache.write(auth_cache_key, test_access_token)

      auth_request = stub_request(:post, "#{talkdesk_config[:auth_base_url]}/oauth/token")

      expected_headers = {
        'Authorization' => "Bearer #{test_access_token}"
      }
      target_action_request = success_stub_request.with(headers: expected_headers)

      subject

      expect(auth_request).not_to have_been_requested
      expect(target_action_request).to have_been_requested
    end

    it 'generates a new access token and caches it when none is available in the cache' do
      auth_url = "#{talkdesk_config[:auth_base_url]}/oauth/token"
      auth_request = stub_request(:post, auth_url).to_return(body: { access_token: test_access_token }.to_json, headers: { 'Content-Type': 'application/json' })

      expected_headers = {
        'Authorization' => "Bearer #{test_access_token}"
      }
      target_action_request = success_stub_request.with(headers: expected_headers)

      subject

      expect(auth_request).to have_been_requested
      expect(target_action_request).to have_been_requested

      cached_token = Rails.cache.read(auth_cache_key)
      expect(cached_token).to eq(test_access_token)
    end
  end

  shared_examples 'for when the Talkdesk API returns an error' do |error_class, faraday_error_class|
    it "raises a #{error_class} with the correct details" do
      expect { subject }.to raise_error(error_class) do |error|
        expect(error.wrapped_exception).to be_a(faraday_error_class)
        expect(error.message).to match(
          klass: faraday_error_class.to_s,
          message: /the server responded with status #{status}/,
          response_body: '',
          response_status: status
        )
      end
    end
  end

  describe '#add_record_to_list' do
    let(:talkdesk_id) { SecureRandom.uuid }
    subject { Clients::TalkdeskApi.new.add_record_to_list(talkdesk_id, record_args) }
    let(:record_args) do
      {
        first_name: Faker::Name.first_name,
        last_name: Faker::Name.last_name,
        phone_number: '**********',
        priority: 7,
        timezone: 'America/Los_Angeles'
      }
    end

    let(:extra_args) do
      {
        activation_code: 'Wv1F5Q',
        debt_resolution_provider: service_entity,
        landing_lead_id: SecureRandom.uuid
      }
    end

    let(:response_body) { { 'id' => SecureRandom.uuid } }
    let(:success_stub_request) do
      create_record_url = "#{talkdesk_config[:base_url]}/record-lists/#{talkdesk_id}/records"
      stub_request(:post, create_record_url).to_return(status: 201, body: response_body.to_json)
    end

    it_behaves_like 'a cached authentication operation'

    context 'with a cached auth token' do
      let(:expected_request_body) do
        {
          first_name: record_args[:first_name],
          last_name: record_args[:last_name],
          phone_numbers: [{
            phone_number: "+1#{record_args[:phone_number]}",
            phone_type: 'MOBILE'
          }],
          priority: 7,
          timezone: record_args[:timezone],
          extra_data: {}
        }
      end

      before do
        Rails.cache.write(auth_cache_key, test_access_token)
      end

      context 'when it has only params' do
        it 'sends a request to Talkdesk to create a record in the leads list' do
          create_record_request = success_stub_request.with(body: expected_request_body.to_json)
          subject
          expect(create_record_request).to have_been_requested
        end
      end

      context 'when it has params and extra_data' do
        subject { Clients::TalkdeskApi.new.add_record_to_list(talkdesk_id, record_args, extra_data) }

        let(:extra_data) do
          {
            activation_code: record_args[:activation_code],
            debt_resolution_provider: record_args[:debt_resolution_provider],
            landing_lead_id: record_args[:landing_lead_id]
          }
        end

        it 'sends a request to Talkdesk to create a record in the leads list' do
          expected_request_body[:extra_data] = extra_data
          create_record_request = success_stub_request.with(body: expected_request_body.to_json)
          subject
          expect(create_record_request).to have_been_requested
        end
      end

      context 'when the Talkdesk API returns a bad request error' do
        before do
          stub_request(:post, %r{#{talkdesk_config[:base_url]}/record-lists/#{talkdesk_id}/records})
            .to_return(status:)
        end

        let(:status) { 400 }

        include_examples 'for when the Talkdesk API returns an error', Clients::TalkdeskApi::BadRequestError, Faraday::BadRequestError
      end

      context 'when the Talkdesk API returns an API error other than bad request' do
        before do
          stub_request(:post, %r{#{talkdesk_config[:base_url]}/record-lists/#{talkdesk_id}/records})
            .to_return(status:)
        end

        let(:status) { 500 }

        include_examples 'for when the Talkdesk API returns an error', Clients::TalkdeskApi::Error, Faraday::ServerError
      end
    end
  end

  describe '#delete_record_from_list' do
    let(:talkdesk_id) { SecureRandom.uuid }
    let(:phone_number) { '**********' }
    subject { Clients::TalkdeskApi.new.delete_record_from_list(talkdesk_id, phone_number) }

    # Use let! to ensure the stub request is set up before each test runs
    let(:status) { 204 }
    let!(:success_stub_request) do
      delete_record_url = "#{talkdesk_config[:base_url]}/record-lists/#{talkdesk_id}/records"
      stub_request(:delete, delete_record_url)
        .with(query: { phone_number: "+1#{phone_number}" })
        .to_return(status:)
    end

    it_behaves_like 'a cached authentication operation'

    context 'with a cached auth token' do
      before do
        Rails.cache.write(auth_cache_key, test_access_token)
      end

      it 'sends a DELETE request to Talkdesk to remove a record from the list and returns true' do
        expect(subject).to be true
        expect(success_stub_request).to have_been_requested.once
      end

      context 'when Talkdesk returns a not found error' do
        let(:status) { 404 }

        before do
          allow(Rails.logger).to receive(:info)
        end

        it 'logs and returns false' do
          expect(subject).to be false
          expect(Rails.logger).to have_received(:info).with('Ignoring not found response when deleting Talkdesk record', talkdesk_list_id: talkdesk_id, phone_number:)
        end

        context 'when raise_not_found option is true' do
          subject { Clients::TalkdeskApi.new.delete_record_from_list(talkdesk_id, phone_number, raise_not_found: true) }

          it 'raises the error' do
            expect { subject }.to raise_error(Clients::TalkdeskApi::Error) do |error|
              expect(error.wrapped_exception).to be_a(Faraday::ResourceNotFound)
            end
          end
        end
      end

      context 'when the Talkdesk API returns an error' do
        before do
          stub_request(:delete, %r{#{talkdesk_config[:base_url]}/record-lists/#{talkdesk_id}/records})
            .with(query: { phone_number: "+1#{phone_number}" })
            .to_return(status:)
        end

        let(:status) { 400 }

        include_examples 'for when the Talkdesk API returns an error', Clients::TalkdeskApi::Error, Faraday::BadRequestError
      end
    end
  end

  describe '#delete_record_from_list_by_id' do
    let(:talkdesk_list_id) { SecureRandom.uuid }
    let(:talkdesk_record_id) { SecureRandom.uuid }
    subject { Clients::TalkdeskApi.new.delete_record_from_list_by_id(talkdesk_list_id, talkdesk_record_id) }

    # Use let! to ensure the stub request is set up before each test runs
    let(:status) { 204 }
    let!(:success_stub_request) do
      delete_record_url = "#{talkdesk_config[:base_url]}/record-lists/#{talkdesk_list_id}/records/#{talkdesk_record_id}"
      stub_request(:delete, delete_record_url).to_return(status:)
    end

    it_behaves_like 'a cached authentication operation'

    context 'with a cached auth token' do
      before do
        Rails.cache.write(auth_cache_key, test_access_token)
      end

      it 'sends a DELETE request to Talkdesk to remove a record from the list and returns true' do
        expect(subject).to be true
        expect(success_stub_request).to have_been_requested.once
      end

      context 'when Talkdesk returns a not found error' do
        let(:status) { 404 }

        before do
          allow(Rails.logger).to receive(:info)
        end

        it 'logs and returns false' do
          expect(subject).to be false
          expect(Rails.logger).to have_received(:info).with('Ignoring not found response when deleting Talkdesk record', talkdesk_list_id:, talkdesk_record_id:)
        end

        context 'when raise_not_found option is true' do
          subject { Clients::TalkdeskApi.new.delete_record_from_list_by_id(talkdesk_list_id, talkdesk_record_id, raise_not_found: true) }

          it 'raises the error' do
            expect { subject }.to raise_error(Clients::TalkdeskApi::Error) do |error|
              expect(error.wrapped_exception).to be_a(Faraday::ResourceNotFound)
            end
          end
        end
      end

      context 'when the Talkdesk API returns an error' do
        before do
          stub_request(:delete, %r{#{talkdesk_config[:base_url]}/record-lists/#{talkdesk_list_id}/records/#{talkdesk_record_id}})
            .to_return(status:)
        end

        let(:status) { 400 }

        include_examples 'for when the Talkdesk API returns an error', Clients::TalkdeskApi::Error, Faraday::BadRequestError
      end
    end
  end

  describe '#each_do_not_call_list' do
    let(:do_not_call_list_id1) { SecureRandom.uuid }
    let(:do_not_call_list_id2) { SecureRandom.uuid }
    let(:do_not_call_lists) do
      {
        '_embedded' => {
          'do_not_call_lists' => [
            { 'id' => do_not_call_list_id1 },
            { 'id' => do_not_call_list_id2 }
          ]
        }
      }
    end

    let(:do_not_call_lists_url) { "#{talkdesk_config[:base_url]}/do-not-call-lists" }
    let(:do_not_call_list1_url) { "#{talkdesk_config[:base_url]}/do-not-call-lists/#{do_not_call_list_id1}/entries" }
    let(:do_not_call_list2_url) { "#{talkdesk_config[:base_url]}/do-not-call-lists/#{do_not_call_list_id2}/entries" }

    let(:headers) { { 'Content-Type' => 'application/json' } }

    subject { Clients::TalkdeskApi.new }

    def do_not_call_entries(count, total_pages)
      {
        'total_pages' => total_pages,
        '_embedded' => {
          'entries' => count.times.map { { 'id' => SecureRandom.uuid } }
        }
      }
    end

    before do
      Rails.cache.write(auth_cache_key, test_access_token)
    end

    it 'iterates through available do not call lists' do
      stub_request(:get, do_not_call_lists_url).and_return(body: do_not_call_lists.to_json, headers: { 'Content-Type' => 'application/json' })

      list1_part1 = do_not_call_entries(100, 2)
      list1_part1_request = stub_request(:get, do_not_call_list1_url).with(query: { per_page: 100, page: 1 }).and_return(body: list1_part1.to_json, headers:)

      list1_part2 = do_not_call_entries(50, 2)
      list1_part2_request = stub_request(:get, do_not_call_list1_url).with(query: { per_page: 100, page: 2 }).and_return(body: list1_part2.to_json, headers:)

      list2 = do_not_call_entries(25, 1)
      list2_request = stub_request(:get, do_not_call_list2_url).with(query: { per_page: 100, page: 1 }).and_return(body: list2.to_json, headers:)

      each_do_not_call_list_args = [
        list1_part1.dig('_embedded', 'entries'),
        list1_part2.dig('_embedded', 'entries'),
        list2.dig('_embedded', 'entries')
      ]
      expect do |block|
        subject.each_do_not_call_list(&block)
      end.to yield_successive_args(*each_do_not_call_list_args)

      expect(list1_part1_request).to have_been_requested
      expect(list1_part2_request).to have_been_requested
      expect(list2_request).to have_been_requested
    end

    it 'does not continue iteration when total page data is not provided' do
      stub_request(:get, do_not_call_lists_url).and_return(body: do_not_call_lists.to_json, headers: { 'Content-Type' => 'application/json' })

      list1 = do_not_call_entries(100, nil)
      list1_request = stub_request(:get, do_not_call_list1_url).with(query: { per_page: 100, page: 1 }).and_return(body: list1.to_json, headers:)

      list2 = do_not_call_entries(25, 'blah')
      list2_request = stub_request(:get, do_not_call_list2_url).with(query: { per_page: 100, page: 1 }).and_return(body: list2.to_json, headers:)

      each_do_not_call_list_args = [
        list1.dig('_embedded', 'entries'),
        list2.dig('_embedded', 'entries')
      ]

      expect do |block|
        subject.each_do_not_call_list(&block)
      end.to yield_successive_args(*each_do_not_call_list_args)

      expect(list1_request).to have_been_requested
      expect(list2_request).to have_been_requested
    end

    context 'when the Talkdesk API returns an error' do
      subject { Clients::TalkdeskApi.new.each_do_not_call_list }
      before { stub_request(:get, %r{#{talkdesk_config[:base_url]}/do-not-call-lists}).to_return(status:) }

      let(:status) { 400 }

      include_examples 'for when the Talkdesk API returns an error', Clients::TalkdeskApi::Error, Faraday::BadRequestError
    end
  end
end
