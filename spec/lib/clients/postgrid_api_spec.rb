# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Clients::PostgridApi do
  let(:api_key) { postgrid_config[:api_key] }
  let(:postgrid_config) { Rails.application.config_for(:postgrid_api) }

  describe '.create_contact' do
    let(:create_contact) do
      Clients::PostgridApi::CreateContact.new(
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        address_line1: '123 Main St',
        address_line2: nil,
        city: 'Chicago',
        state: 'IL',
        zip_code: '60625'
      )
    end
    let(:response_payload) do
      {
        id: 'contact_h5Acm6TTeRUfNAPQaHQFBd',
        object: 'contact',
        live: false,
        addressLine1: '123 MAIN ST',
        addressStatus: 'verified',
        city: 'CHICAGO',
        country: 'UNITED STATES',
        countryCode: 'US',
        firstName: 'Erica',
        lastName: 'Lambert',
        mailingLists: [],
        postalOrZip: '60606',
        provinceOrState: 'IL',
        createdAt: '2024-08-08T22:08:45.054Z',
        updatedAt: '2024-08-12T22:34:07.965Z'
      }
    end

    before do
      stub_request(:post, 'https://api.postgrid.com/print-mail/v1/contacts')
        .with(headers: { 'Content-Type' => 'application/json', 'x-api-key': api_key })
        .and_return(body: response_payload.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it 'triggers the expected request to the PostGrid API' do
      described_class.create_contact(create_contact)

      request_body = {
        firstName: create_contact.first_name,
        lastName: create_contact.last_name,
        addressLine1: create_contact.address_line1,
        addressLine2: create_contact.address_line2,
        city: create_contact.city,
        provinceOrState: create_contact.state,
        postalOrZip: create_contact.zip_code,
        countryCode: 'US'
      }
      expect(WebMock).to have_requested(:post, 'https://api.postgrid.com/print-mail/v1/contacts')
        .with(body: request_body.to_json)
        .once
    end

    context 'when the request is successful' do
      it 'returns a valid Contact' do
        contact = described_class.create_contact(create_contact)
        expect(contact).to be_a(Clients::PostgridApi::Contact)
        expect(contact).to be_valid
        expect(contact.id).to eq(response_payload[:id])
      end
    end

    context 'when an incomplete/invalid response is returned' do
      before do
        stub_request(:post, 'https://api.postgrid.com/print-mail/v1/contacts')
          .and_return(body: {}.to_json, headers: { 'Content-Type' => 'application/json' })
      end

      it 'raises an InvalidResponse error' do
        expect { described_class.create_contact(create_contact) }.to raise_error(Clients::PostgridApi::InvalidResponse) do |error|
          expect(error.message).to include('Id is required')
        end
      end
    end

    context 'when the request fails' do
      let(:error_response_body) { { object: 'error', error: { type: 'validation_error', message: 'Failed to satisfy the following constraints: addressLine1 should not be empty' } } }

      before do
        stub_request(:post, 'https://api.postgrid.com/print-mail/v1/contacts')
          .and_return(status: 400,
                      body: error_response_body.to_json,
                      headers: { 'Content-Type' => 'application/json' })
        allow(Rails.logger).to receive(:error)
      end

      it 'raises a client error and logs response details' do
        expect { described_class.create_contact(create_contact) }.to raise_error(Clients::PostgridApi::Error) do |error|
          expect(error.response[:body]).to eq(error_response_body.as_json)
        end

        message = {
          klass: 'Faraday::BadRequestError',
          message: /the server responded with status 400/,
          response_body: error_response_body.as_json,
          response_status: 400
        }

        expect(Rails.logger).to have_received(:error).with('PostGrid API error.', message)
      end
    end
  end

  describe '.send_pdf_letter' do
    let(:file) { Tempfile.new }
    let(:send_pdf_letter) { Clients::PostgridApi::SendPdfLetter.new(file:, recipient_contact_id: 'contact_h5Acm6TTeRUfNAPQaHQFBd', filename: 'test-filename', description: 'test-description') }
    let(:above_contact_id) { 'contact_b3PDydAF6NEm32gnr2tGiT' }
    let(:response_payload) do
      {
        id: 'letter_6dqkq7maiXvZ3wLhsaPQ6P',
        object: 'letter',
        live: false,
        addressPlacement: 'insert_blank_page',
        attachedPDF: nil,
        carrierTracking: nil,
        color: true,
        doubleSided: true,
        envelope: 'standard',
        envelopeType: 'standard_double_window',
        express: false,
        from: { id: 'contact_b3PDydAF6NEm32gnr2tGiT', object: 'contact' },
        mailingClass: 'first_class',
        sendDate: '2024-08-14T16:31:23.865Z',
        size: 'us_letter',
        status: 'ready',
        to: { id: 'contact_h5Acm6TTeRUfNAPQaHQFBd', object: 'contact' },
        uploadedPDF: 'https://pg-prod-bucket-1.s3.amazonaws.com/test/pdf_2GueUb2JPYYZtmb5mnrUfg?AWSAccessKeyId=AKIA5GFUILSULWTWCR64&Expires=1723653983&Signature=bajpj3BlY3Tecd76DwuQEROfpl8%3D',
        createdAt: '2024-08-14T16:31:23.867Z',
        updatedAt: '2024-08-14T16:31:23.867Z'
      }
    end

    before do
      stub_request(:post, 'https://api.postgrid.com/print-mail/v1/letters')
        .with(headers: { 'Content-Type' => %r{^multipart/form-data}, 'x-api-key': api_key })
        .and_return(body: response_payload.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it 'triggers the expected request to the PostGrid API' do
      file_part = double(Faraday::Multipart::FilePart)
      allow(Faraday::Multipart::FilePart).to receive(:new).with(send_pdf_letter.file, 'application/pdf', 'test-filename').and_return(file_part)

      expected_body = {
        pdf: file_part,
        to: 'contact_h5Acm6TTeRUfNAPQaHQFBd',
        from: Rails.application.config_for(:postgrid_api).above_contact_id,
        doubleSided: true,
        color: true,
        envelopeType: 'standard_double_window',
        mailingClass: 'first_class',
        addressPlacement: 'insert_blank_page',
        description: 'test-description'
      }

      # WebMock doesn't support matching a form/multipart body, so we resort to asserting that the client
      # passed the expected body to its private `post` method
      expect(Clients::PostgridApi).to receive(:post).with(anything, expected_body, anything).and_call_original

      described_class.send_pdf_letter(send_pdf_letter)

      expect(WebMock).to have_requested(:post, 'https://api.postgrid.com/print-mail/v1/letters').once
    end

    context 'when the request is successful' do
      it 'returns a valid Letter' do
        link_token_result = described_class.send_pdf_letter(send_pdf_letter)
        expect(link_token_result).to be_a(Clients::PostgridApi::Letter)
        expect(link_token_result).to be_valid
        expect(link_token_result.id).to eq(response_payload[:id])
      end
    end

    context 'when an incomplete/invalid response is returned' do
      before do
        stub_request(:post, 'https://api.postgrid.com/print-mail/v1/letters')
          .and_return(body: {}.to_json, headers: { 'Content-Type' => 'application/json' })
      end

      it 'raises an InvalidResponse error' do
        expect { described_class.send_pdf_letter(send_pdf_letter) }.to raise_error(Clients::PostgridApi::InvalidResponse) do |error|
          expect(error.message).to include('Id is required')
        end
      end
    end

    context 'when the request fails' do
      let(:error_response_body) { { object: 'error', error: { type: 'validation_error', message: "Failed to satisfy the following constraints: 'pdf' is not a file or URL" } } }

      before do
        stub_request(:post, 'https://api.postgrid.com/print-mail/v1/letters')
          .and_return(status: 400,
                      body: error_response_body.to_json,
                      headers: { 'Content-Type' => 'application/json' })
        allow(Rails.logger).to receive(:error)
      end

      it 'raises a client error and logs response details' do
        expect { described_class.send_pdf_letter(send_pdf_letter) }.to raise_error(Clients::PostgridApi::Error) do |error|
          expect(error.response[:body]).to eq(error_response_body.as_json)
        end

        message = {
          klass: 'Faraday::BadRequestError',
          message: /the server responded with status 400/,
          response_body: error_response_body.as_json,
          response_status: 400
        }

        expect(Rails.logger).to have_received(:error).with('PostGrid API error.', message)
      end
    end
  end
end
