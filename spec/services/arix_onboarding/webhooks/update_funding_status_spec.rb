# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ArixOnboarding::Webhooks::UpdateFundingStatus do
  include NotifierHelper

  let!(:arix_funding_status) { create(:arix_funding_status, arix_loan_id:, funding_status: nil, submission_status: nil, validation_errors:) }
  let(:arix_loan_id) { SecureRandom.uuid }
  let(:arix_loan_status) { Loan::NOT_FULLY_FUNDED }
  let(:arix_status_code) { 104 }
  let(:loan_status_params) do
    {
      Id: 0,
      LoanId: arix_loan_id,
      Status: arix_status_code,
      TimeStamp: '2022-05-30T03:17:43.6233616+00:00',
      DateInserted: '0001-01-01T00:00:00+00:00'
    }
  end
  let(:validation_errors) { [] }

  before do
    allow(Rails.logger).to receive(:info).and_call_original
    allow(Rails.logger).to receive(:warn).and_call_original
    allow(Rails.logger).to receive(:error).and_call_original
  end

  subject { described_class.call(loan_status_params: loan_status_params.with_indifferent_access) }

  describe '.call' do
    context 'when the arix callback has success response' do
      it 'saves the funding status' do
        subject
        expect(arix_funding_status.reload.funding_status).to eq(arix_loan_status)
      end

      context 'when the arix status is not found in our mappings' do
        let(:arix_status_code) { 999 }

        it 'raises an exception' do
          expect { subject }.to raise_error(described_class::InvalidStatusError, 'Invalid Status received from Arix: 999')
        end
      end
    end

    context 'when the arix callback has failed response' do
      before { allow(AmsSlackBot).to receive(:post_message_blocks) }

      let(:arix_loan_status) { Loan::COMPLIANCE_FAILED }
      let(:created_date) { Date.today.to_s }
      let(:failed_rule_reasons) do
        [
          {
            RuleName: 'Not a real failure',
            Rule: 'All uploaded files must be in English',
            Data: 'deutsche bank - Kontoauszug.pdf',
            Result: true,
            FailedComplianceID: 67
          },
          {
            RuleName: 'A real rule',
            Rule: 'All uploaded files must be PDFs',
            Data: 'Final_Final_FINAL_Version_Dont_Delete.docx',
            Result: false,
            FailedComplianceID: 3567
          },
          {
            RuleName: 'Another real rule',
            Rule: 'Files could not be read',
            Data: 'Top_Secret_Strategy_To_Become_A_Millionaire.txt',
            Result: false,
            FailedComplianceID: 243
          }
        ]
      end
      let(:loan_status_params) do
        {
          MPLId: 'xxx',
          LoanId: arix_loan_id,
          CreateDate: created_date,
          FailedRulesReasons: failed_rule_reasons
        }
      end
      let(:contract_date) { '03/26/2025' }
      let!(:loanpro_loan) { create(:loanpro_loan, loan: arix_funding_status.loan, til_sign_date: Time.now) }
      let!(:til_history) do
        til_data = { loan: { contractDate: contract_date, loanProLoansId: loanpro_loan.id } }.as_json
        create(:til_history, loan: arix_funding_status.loan, signed_at: loanpro_loan.til_sign_date, til_data:)
      end

      it 'saves the validation errors' do
        subject

        arix_funding_status.reload
        expect(Rails.logger).to have_received(:warn).with('COMPLIANCE_FAILED', arix_loan_id:)
        expect(arix_funding_status.funding_status).to eq(arix_loan_status)
        expect(arix_funding_status.validation_errors[0]['rules'][0]['name']).to eq('A real rule')
        expect(arix_funding_status.validation_errors[0]['rules'][1]['name']).to eq('Another real rule')
      end

      describe 'slack alerting' do
        it 'sends notification via slack' do
          subject

          expect(AmsSlackBot).to have_received(:post_message_blocks) do |message_blocks:, channel:|
            expect(message_blocks.to_s).to include('Arix Compliance Failure')
            expect(message_blocks.to_s).to include(arix_funding_status.loan.unified_id)
            expect(message_blocks.to_s).to include('A real rule, Another real rule')
            expect(message_blocks.to_s).to include(contract_date)
            expect(message_blocks.to_s).to include("https://dash-sandbox.abovelending.com/admin/ams_funding/#{arix_funding_status.id}/arix_submission_details")

            expect(channel).to eq(Rails.application.config_for(:slack_channels).crb_onboarding_failures_channel)
          end
        end

        context 'when there is an error retrieving the contract date' do
          before do
            loanpro_loan.update!(til_sign_date: nil)
            til_history.update!(signed_at: nil)
          end

          it 'sends notification via slack' do
            subject

            expect(AmsSlackBot).to have_received(:post_message_blocks) do |message_blocks:, channel:|
              expect(message_blocks.to_s).to include('Unable to retrieve contract date')
              expect(channel).to eq(Rails.application.config_for(:slack_channels).crb_onboarding_failures_channel)
            end
          end
        end
      end

      context 'when the error already exists' do
        let(:validation_errors) do
          [
            {
              type: 'External',
              status: 'Fail',
              description: 'Loan failed compliance ruleset',
              completed_at: created_date,
              rules: [
                {
                  status: 'Fail',
                  name: 'An existing rule',
                  description: 'Files could not be read',
                  data: 'Top_Secret_Strategy_To_Become_A_Millionaire.txt',
                  result: false,
                  run_at: created_date
                }
              ]
            }
          ]
        end

        it 'does not save duplicate validation error' do
          subject

          arix_funding_status.reload
          expect(arix_funding_status.funding_status).to be_nil
          expect(arix_funding_status.validation_errors.count).to eq(1)
          expect(arix_funding_status.validation_errors[0]['rules'][0]['name']).to eq('An existing rule')

          expect(Rails.logger).to have_received(:info).with('This failure reason has already been processed for this loan', arix_loan_id:)
        end
      end

      context 'when the failed reasons are missing' do
        let(:loan_status_params) do
          {
            MPLId: 'xxx',
            LoanId: arix_loan_id,
            CreateDate: created_date
          }
        end

        it 'logs warning and exits' do
          subject

          arix_funding_status.reload
          expect(arix_funding_status.funding_status).to be_nil
          expect(Rails.logger).to have_received(:warn).with('Unable to process Arix webhook payload', loan_status_params:)
        end
      end
    end
  end
end
