# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Ams::Api::Loans::FinalDecision, type: :service do
  include_context 'service with authentication'

  let(:pending_loan_status_name) { 'PENDING' }
  let(:approved_loan_status_name) { 'APPROVED' }
  let(:back_end_decline_status_name) { 'BACK_END_DECLINED' }
  let(:decision_reason_number) { '1' }
  let(:decline_reason_text) { '"Unable to verify identity"2' }
  let(:decline_reasons) { ['Unable to verify identity'] }
  let(:credit_score) { 617 }
  let(:score_factor) { 'Serious delinquency; Proportion of balances to credit limits is too high on bank revolving or other revolving accounts; Number of accounts with delinquency; Length of time accounts have been established' }
  let(:originating_party) { 'CRB' }
  let(:request_id) { loan.request_id }

  let(:approved_loan_app_status) { LoanAppStatus.for(:approved) }
  let(:onboarded_loan_app_status) { LoanAppStatus.for(:onboarded) }
  let!(:declined_loan_app_status) { LoanAppStatus.for(:back_end_declined) }
  let(:pending_loan_app_status) { LoanAppStatus.for(:pending) }
  let(:ineligible_loan_status) { LoanAppStatus.for(:funded) }
  let(:new_loan_status) { LoanAppStatus.for(:new) }
  let!(:loan) { create(:ipl_loan, loan_app_status: pending_loan_app_status) }
  let!(:lead) { create(:lead, code: loan.code) }
  let!(:ineligible_loan) { create(:ipl_loan, loan_app_status: ineligible_loan_status) }
  let!(:new_loan) { create(:ipl_loan, loan_app_status: new_loan_status) }
  let!(:borrower) { loan.borrower }
  let!(:borrower_additional_info) { create(:borrower_additional_info, borrower:, loan_id: loan.id) }
  let(:todo) { create(:todo, loan:) }
  let!(:todo_doc) { create(:todo_doc, todo:) }
  let!(:bank_account) { create(:bank_account, enabled: false, borrower:, loan:) }
  let(:service_entity) { 'bf' }
  let(:contract_link) do
    base_url = Rails.application.config_for(:general).lander_base_url
    "#{base_url}/loan-app/contract/#{loan.reload.contract_signing_token}?s=#{service_entity}"
  end

  before do
    Current.oauth_token = 'token'
    allow(Clients::CommunicationsServiceApi).to receive(:send_message!)
  end

  describe '#call' do
    before { stub_request(:patch, "http://beyond.com/api/lending/programs/#{loan.program_id}/loan") }

    # before do
    #   stub_const('Ams::Api::Loans::FinalDecision::BF_SERVICE_ENTITY', 'bf')
    #   stub_const('Ams::Api::Loans::FinalDecision::FLLG_SERVICE_ENTITY', 'fllg')
    # end

    context 'when the loan is ready to be approved' do
      let(:service_object) { described_class.new(loan_status: approved_loan_status_name, request_id: loan.request_id) }

      it 'returns a 400 response when the loan does not have a bank account registered' do
        # The bank account associated with the loan is created above with `enabled: false` and therefore can't be used.
        # Let's create an enabled bank account associated with a different loan - this account also should not be used.
        other_loan = create(:ipl_loan, borrower:, created_at: 10.months.ago)
        create(:bank_account, borrower:, loan: other_loan, created_at: 10.months.ago)

        service_object.call

        expect(service_object.body).to \
          eq({
               error: 'Bad Request',
               message: "Operation not valid. There is not bank account linked to the loan with id. #{loan.unified_id}",
               statusCode: 400
             })
      end

      context 'when the request is successful' do
        let(:todo) { create(:todo, status: :approved, loan:) }
        let(:bank_account) { create(:bank_account, borrower: loan.borrower, loan:) }

        before do
          allow(Onboarding::SyncEligibilityData).to receive(:call).with({ loan_id: loan.id })
          allow(Gds::TriggerTodoResyncJob).to receive(:perform_async).with(loan.request_id)
          allow(Contracts::GenerateContractJob).to receive(:perform_async)
          allow(Contracts::SendContractApprovalSmsJob).to receive(:perform_async)
        end

        it 'returns an ok response' do
          service_object.call
          expect(service_object.body).to \
            eq({ message: 'ok' })
        end

        it 'syncs eligibility data' do
          service_object.call
          expect(Onboarding::SyncEligibilityData).to have_received(:call).with(loan_id: loan.id)
        end

        it 'updates the loan status to approved' do
          expect { service_object.call }.to change { loan.reload.loan_app_status.name }.from('PENDING').to('APPROVED')
        end

        it 'generates a contract' do
          service_object.call

          expect(Contracts::GenerateContractJob).to have_received(:perform_async).with(loan.id)
        end

        it 'triggers the SendContractApprovalSms job' do
          service_object.call

          expect(Contracts::SendContractApprovalSmsJob).to have_received(:perform_async).with(loan.id, contract_link)
        end

        it 'queues up Loans::TodoSyncJob' do
          service_object.call
          expect(Gds::TriggerTodoResyncJob).to have_received(:perform_async).with(loan.request_id)
        end

        context 'when the loan has NO contract signing token' do
          it 'assigns a contract signing token to the loan' do
            service_object.call
            expect(loan.reload.contract_signing_token).to be_present
          end
        end

        context 'when the loan already has a contract signing token' do
          let(:contract_signing_token) { SecureRandom.uuid }

          before { loan.update!(contract_signing_token:) }

          it 'leaves the contract signing token for the loan unchanged if it doesn\'t already have one' do
            service_object.call
            expect(loan.reload.contract_signing_token).to eq(contract_signing_token)
          end
        end

        it 'sends email' do
          service_object.call
          base_url = Rails.application.config_for(:general).lander_base_url
          expect(Clients::CommunicationsServiceApi).to have_received(:send_message!).with(
            recipient: loan.borrower.email,
            template_key: 'loan_approved', # Must match key for this template in Communications Service
            inputs: {
              first_name: loan.borrower.first_name,
              link: "#{base_url}/loan-app/contract/#{loan.reload.contract_signing_token}?s=#{service_entity}"
            }
          )
        end
      end

      context 'when the snowflake eligibility lookup fails' do
        let(:bank_account) { create(:bank_account, borrower: loan.borrower, loan:) }

        before do
          allow(Onboarding::SyncEligibilityData).to receive(:call).and_raise(BeyondEligibility::LookupError)
        end

        it 'returns a 500 response', :aggregate_failures do
          service_object.call

          error_body = {
            statusCode: 500,
            error: 'Internal Server Error',
            message: "Unable to fetch eligibility data for loan with requestId #{loan.request_id}"
          }

          expect(service_object.status).to eq(500)
          expect(service_object.body).to eq(error_body)
        end

        it 'does not update the loan status to approved' do
          service_object.call
          expect(loan.reload.loan_app_status.name).to eq('PENDING')
        end
      end
    end

    context 'when the loan is back end declined' do
      context 'when the request is successful' do
        let(:declined_params) do
          {
            request_id: loan.request_id,
            loan_status: back_end_decline_status_name,
            decision_reason_number:,
            decline_reason_text:,
            decline_reasons:,
            credit_score:,
            score_factor:,
            originating_party:
          }
        end
        let(:service_object) { described_class.new(**declined_params) }

        before do
          allow(LoanApplications::BackEndDecline).to receive(:call)
        end

        context 'when the borrower is no longer eligible' do
          before { lead.update!(expiration_date: Time.now - 5.minutes) }

          it 'does not raise any error and continues with back end decline' do
            service_object.call
            expect(service_object.body).to eq({ message: 'ok' })
            expect(service_object.status).to eq(200)
          end
        end

        it 'returns an ok response' do
          service_object.call
          expect(service_object.body).to eq({ message: 'ok' })
          expect(service_object.status).to eq(200)
        end

        it 'completes the back end decline' do
          service_object.call
          expect(LoanApplications::BackEndDecline).to have_received(:call).with(loan:, decision_reason_number:, decline_reason_text:,
                                                                                decline_reasons:, credit_score:, score_factor:)
        end
      end
    end

    context 'when the pre-existing loan status matches the status on the request' do
      let(:approved_loan_status) { LoanAppStatus.for(:approved) }
      let!(:loan) { create(:ipl_loan, loan_app_status: approved_loan_status) }
      let!(:bank_account) { create(:bank_account, borrower: loan.borrower, loan:) }

      it 'returns success and does not go through the other steps of the approved or declined flow' do
        service_object = described_class.new(loan_status: approved_loan_status_name, request_id: loan.request_id)

        service_object.call

        expect(service_object.body).to eq({ message: 'ok' })
        expect(service_object.status).to eq(200)
      end
    end

    it 'returns a 404 if the loan_status param is nil' do
      service_object = described_class.new(request_id: loan.request_id, loan_status: nil)
      service_object.call
      expect(service_object.body).to include({ message: 'LoanAppStatus not found for the given name' })
      expect(service_object.status).to eq(404)
    end

    it 'returns a 400 if the loan_status param is an invalid loan_app_status' do
      service_object = described_class.new(request_id: loan.request_id, loan_status: 'invalid status')
      service_object.call
      expect(service_object.body).to include({ message: '"status" must be one of [BACK_END_DECLINED, APPROVED]' })
      expect(service_object.status).to eq(400)
    end

    it 'returns a 400 if the loan_status param is not APPROVED or BACK_END_DECLINED' do
      service_object = described_class.new(request_id: loan.request_id, loan_status: new_loan_status.name)
      service_object.call
      expect(service_object.body).to include({ message: '"status" must be one of [BACK_END_DECLINED, APPROVED]' })
      expect(service_object.status).to eq(400)
    end

    it 'returns a 404 if the loan cannot be found by the request_id' do
      service_object = described_class.new(loan_status: pending_loan_status_name, request_id: 0)
      service_object.call
      expect(service_object.body).to include({ message: 'there is no eligible loan by the given request_id' })
      expect(service_object.status).to eq(404)
    end

    it 'returns a 404 when the loan is not eligible' do
      service_object = described_class.new(request_id: ineligible_loan.request_id, loan_status: approved_loan_status_name)
      service_object.call
      expect(service_object.body).to eq({ error: 'Not Found', message: 'there is no eligible loan by the given request_id', statusCode: 404 })
      expect(service_object.status).to eq(404)
    end

    it 'returns a 409 when another loan with the code is approved' do
      create(:ipl_loan, code: lead.code, loan_app_status: approved_loan_app_status)
      service_object = described_class.new(request_id: loan.request_id, loan_status: approved_loan_status_name)

      service_object.call
      expect(service_object.body).to eq({ error: 'Conflict', message: "there is already a loan for the code #{loan.code}", statusCode: 409 })
      expect(service_object.status).to eq(409)
    end

    it 'returns a 409 when another loan with the code is onboarded' do
      create(:ipl_loan, code: lead.code, loan_app_status: onboarded_loan_app_status)
      service_object = described_class.new(request_id: loan.request_id, loan_status: approved_loan_status_name)

      service_object.call
      expect(service_object.body).to eq({ error: 'Conflict', message: "there is already a loan for the code #{loan.code}", statusCode: 409 })
      expect(service_object.status).to eq(409)
    end

    it 'ignores deleted approved and onboarded loans' do
      create(:ipl_loan, code: lead.code, loan_app_status: approved_loan_app_status, deleted_at: Time.now)
      create(:ipl_loan, code: lead.code, loan_app_status: onboarded_loan_app_status, deleted_at: Time.now)
      service_object = described_class.new(request_id: loan.request_id, loan_status: approved_loan_status_name)

      service_object.call
      expect(service_object.status).not_to eq(409)
    end
  end
end
