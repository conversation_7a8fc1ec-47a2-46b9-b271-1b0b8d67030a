# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Ams::Api::Loans::UplFinalDecision, type: :service do
  include ActiveSupport::Testing::TimeHelpers

  include_context 'service with authentication'

  let(:approved_loan_app_status) { create(:loan_app_status, :approved) }
  let(:declined_loan_app_status) { create(:loan_app_status, :back_end_declined) }
  let(:pending_loan_app_status) { create(:loan_app_status, :pending) }
  let!(:loan_product_type) { 'UPL' }

  let!(:loan) { create(:loan, loan_app_status: pending_loan_app_status, product_type: loan_product_type) }
  let!(:request_id) { loan.request_id }
  let!(:loan_inquiry) { create(:loan_inquiry, loan:) }

  before do
    Current.oauth_token = 'token'
  end

  describe '#call' do
    context 'validations' do
      before do |example|
        service_object.call unless example.metadata[:skip_calling_service_object]
      end

      context 'when the loan_status param is nil' do
        let(:service_object) { described_class.new(request_id:, loan_status: nil) }

        it 'returns a 400 with an error message' do
          expect(service_object.body).to include({ message: '"loan_status" is required' })
          expect(service_object.status).to eq(400)
        end
      end

      context 'when the loan is backend declined and decision_reason_number param is nil' do
        let(:service_object) { described_class.new(request_id:, loan_status: declined_loan_app_status.name, decision_reason_number: nil) }

        it 'returns a 400 with an error message' do
          expect(service_object.body).to include({ message: '"decision_reason_number" is required' })
          expect(service_object.status).to eq(400)
        end
      end

      context 'when the loan_status param is an invalid loan_app_status' do
        let(:service_object) { described_class.new(request_id:, loan_status: 'invalid status') }

        it 'returns a 400 with an error message' do
          expect(service_object.body).to include({ message: '"status" must be one of [BACK_END_DECLINED, APPROVED]' })
          expect(service_object.status).to eq(400)
        end
      end

      context 'when the loan_status param is not APPROVED or BACK_END_DECLINED' do
        let(:service_object) { described_class.new(request_id:, loan_status: pending_loan_app_status.name) }

        it 'returns a 400 with an error message' do
          expect(service_object.body).to include({ message: 'Invalid final decision status. Valid statuses: BACK_END_DECLINED, APPROVED' })
          expect(service_object.status).to eq(400)
        end
      end

      context 'when the loan cannot be found by the request_id' do
        let(:service_object) { described_class.new(loan_status: approved_loan_app_status.name, request_id: 0) }

        it 'returns a 404 with an error message' do
          expect(service_object.body).to include({ message: 'Loan with requestId 0 not found' })
          expect(service_object.status).to eq(404)
        end
      end

      context 'when the loan product type is not DM or UPL' do
        let(:loan_product_type) { 'IPL' }
        let(:service_object) { described_class.new(request_id:, loan_status: approved_loan_app_status.name) }

        it 'returns a 400 with an error message' do
          expect(service_object.body).to include({ message: "Unexpected loan product type IPL for loan with id #{loan.id}. Expected DM" })
          expect(service_object.status).to eq(400)
        end
      end

      context 'when the loan is in a expirable status and offers on the loan have been expired', :skip_calling_service_object do
        before do
          create(:offer, loan:, expiration_date: Time.current - 1.hour)
          allow(Clients::GdsApi).to receive(:sync_status)
          allow(Upl::DeliverNoticeOfAdverseActionJob).to receive(:perform_async)

          service_object.call
        end

        let!(:service_object) { described_class.new(request_id:, loan_status: approved_loan_app_status.name) }
        let(:loan) { create(:loan, loan_app_status: approved_loan_app_status, product_type: loan_product_type) }
        let(:expired_loan_status) { create(:loan_app_status, :expired) }

        it 'expires the loan' do
          expect(loan.reload.loan_app_status).to eq(expired_loan_status)
        end

        it 'returns a 400 with an error message' do
          expect(service_object.body).to include(
            { message: "The transition cannot be executed because the loan #{loan.id} is in the status 20 (EXPIRED), " \
                       'but it must be in one of the following: 10 (BANK_SUBMIT), 12 (PENDING), 35 (READY_FOR_REVIEW), ' \
                       '15 (APPROVED), 13 (BACK_END_DECLINED)' }
          )
          expect(service_object.status).to eq(400)
        end
      end

      context 'when current loan app status is not in one of the four: BANK_SUBMIT, PENDING, APPROVED, BACK_END_DECLINED' do
        let(:new_loan_status) { create(:loan_app_status, :new) }
        let(:loan) { create(:loan, loan_app_status: new_loan_status, product_type: loan_product_type) }
        let(:service_object) { described_class.new(request_id:, loan_status: approved_loan_app_status.name) }

        it 'returns a 400 with an error message' do
          expect(service_object.body).to include(
            { message: "The transition cannot be executed because the loan #{loan.id} is in the status 1 (NEW), " \
                       'but it must be in one of the following: 10 (BANK_SUBMIT), 12 (PENDING), 35 (READY_FOR_REVIEW), ' \
                       '15 (APPROVED), 13 (BACK_END_DECLINED)' }
          )
          expect(service_object.status).to eq(400)
        end
      end

      context 'when bank account on loan does not exist of is not enabled' do
        let!(:bank_account) { create(:bank_account, enabled: false, borrower: loan.borrower, loan:) }
        let(:service_object) { described_class.new(request_id:, loan_status: approved_loan_app_status.name) }

        it 'returns a 400 with an error message' do
          expect(service_object.body).to include({ message: "Operation not valid. There is not bank account linked to the loan with id. #{loan.unified_id}" })
          expect(service_object.status).to eq(400)
        end
      end
    end

    context 'when the pre-existing loan status matches the status on the request' do
      before { create(:bank_account, enabled: true, borrower: loan.borrower, loan:) }

      let!(:loan) { create(:loan, loan_app_status: approved_loan_app_status, product_type: loan_product_type) }
      let(:service_object) { described_class.new(loan_status: approved_loan_app_status.name, request_id:) }

      it 'returns success and does not go through the other steps of the approved or declined flow' do
        service_object.call

        expect(service_object.body).to eq({ message: 'ok' })
        expect(service_object.status).to eq(200)
      end
    end

    context 'when the loan is ready to be approved' do
      context 'when the request is successful' do
        let(:service_object) { described_class.new(loan_status: approved_loan_app_status.name, request_id:) }

        before do
          create(:bank_account, enabled: true, borrower: loan.borrower, loan:)
          allow(Clients::CommunicationsServiceApi).to receive(:send_message!)
          allow(Gds::TriggerTodoResyncJob).to receive(:perform_async).with(loan.request_id)
          allow(Contracts::GenerateContractJob).to receive(:perform_async)
        end

        it 'returns an ok response' do
          service_object.call
          expect(service_object.body).to eq({ message: 'ok' })
          expect(service_object.status).to eq(200)
        end

        it 'updates the loan status to approved' do
          expect { service_object.call }.to change { loan.reload.loan_app_status.name }.from('PENDING').to('APPROVED')
        end

        it 'queues up Loans::TodoSyncJob' do
          expect(Gds::TriggerTodoResyncJob).to receive(:perform_async).with(loan.request_id)

          service_object.call
        end

        it 'triggers delivery of approval email' do
          service_object.call

          expect(Clients::CommunicationsServiceApi).to have_received(:send_message!).with(
            recipient: loan.borrower.email,
            template_key: Clients::CommunicationsServiceApi::UPL_LOAN_APPROVED_TEMPLATE,
            inputs: {
              first_name: loan.borrower.first_name
            },
            attribution: Communications::MessageAttribution.call(loan:)
          )
        end

        it 'queues up Contracts::GenerateContractJob' do
          service_object.call

          expect(Contracts::GenerateContractJob).to have_received(:perform_async).with(loan.id)
        end
      end
    end

    context 'when the loan is back end declined' do
      context 'when the request is successful' do
        let(:decision_reason_number) { '1' }
        let(:decline_reason_text) { 'Unable to verify identity' }
        let(:decline_reasons) { ['Unable to verify identity'] }
        let(:credit_score) { 617 }
        let(:score_factor) { 'Serious delinquency; Proportion of balances to credit limits is too high on bank revolving or other revolving accounts; Number of accounts with delinquency; Length of time accounts have been established' }
        let(:declined_params) do
          {
            request_id:,
            loan_status: declined_loan_app_status.name,
            decision_reason_number:,
            decline_reason_text:,
            decline_reasons:,
            credit_score:,
            score_factor:
          }
        end
        let!(:service_object) { described_class.new(**declined_params) }

        before do
          create(:bank_account, enabled: true, borrower: loan.borrower, loan:)
          allow(LoanApplications::BackEndDecline).to receive(:call)
        end

        it 'completes the back end decline' do
          service_object.call
          expect(LoanApplications::BackEndDecline).to have_received(:call).with(loan:, decision_reason_number:, decline_reason_text:,
                                                                                decline_reasons:, credit_score:, score_factor:)
        end

        it 'returns an ok response' do
          service_object.call
          expect(service_object.body).to eq({ message: 'ok' })
          expect(service_object.status).to eq(200)
        end
      end
    end
  end
end
