# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanApplications::Approve, type: :service do
  include ActiveSupport::Testing::TimeHelpers

  describe '.call' do
    subject(:service) { described_class.new(loan:, lead:) }

    let!(:loan) { create(:loan, loan_app_status: LoanAppStatus.for(:pending)) }
    let!(:lead) { create(:lead, code: loan.code) }
    let(:contract_link) do
      base_url = Rails.application.config_for(:general).lander_base_url
      "#{base_url}/loan-app/contract/#{loan.reload.contract_signing_token}?s=bf"
    end

    before do
      allow(Onboarding::SyncEligibilityData).to receive(:call).with({ loan_id: loan.id })
      allow(Contracts::GenerateContractJob).to receive(:perform_async)
      allow(Contracts::SendContractApprovalSmsJob).to receive(:perform_async)
      allow(Clients::CommunicationsServiceApi).to receive(:send_message!)
    end

    it 'syncs eligibility data' do
      service.call
      expect(Onboarding::SyncEligibilityData).to have_received(:call).with(loan_id: loan.id)
    end

    it 'updates the loan status to approved' do
      expect { service.call }.to change { loan.reload.loan_app_status.name }.from('PENDING').to('APPROVED')
    end

    it 'generates a contract' do
      service.call

      expect(Contracts::GenerateContractJob).to have_received(:perform_async).with(loan.id)
    end

    context 'when the loan has NO contract signing token' do
      it 'assigns a contract signing token to the loan' do
        service.call
        expect(loan.reload.contract_signing_token).to be_present
      end
    end

    context 'when the loan already has a contract signing token' do
      let(:contract_signing_token) { SecureRandom.uuid }

      before { loan.update!(contract_signing_token:) }

      it 'leaves the contract signing token for the loan unchanged if it doesn\'t already have one' do
        service.call
        expect(loan.reload.contract_signing_token).to eq(contract_signing_token)
      end
    end

    it 'triggers the SendContractApprovalSms job' do
      service.call

      expect(Contracts::SendContractApprovalSmsJob).to have_received(:perform_async).with(loan.id, contract_link)
    end

    it 'sends the contract notification email' do
      service.call
      expect(Clients::CommunicationsServiceApi).to have_received(:send_message!).with(
        recipient: loan.borrower.email,
        template_key: 'loan_approved', # Must match key for this template in Communications Service
        inputs: { first_name: loan.borrower.first_name, link: contract_link }
      )
    end
  end
end
