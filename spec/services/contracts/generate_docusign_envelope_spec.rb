# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Contracts::GenerateDocusignEnvelope do
  subject(:service) do
    described_class.new(loan:, loan_agreement_document:, supplemental_documents:)
  end

  let(:borrower) { create(:borrower) }
  let(:loan) { create(:loan, borrower:, originating_party: Loan::ORIGINATING_PARTIES[:CRB]) }
  let(:loan_agreement_template) { create(:doc_template, type: DocTemplate::TYPES[:CRB_INSTALLMENT_LOAN_AGREEMENT]) }
  let(:loan_agreement_document) { build(:contract_document, template: loan_agreement_template, content: "PDF-1.4%\nInstallment Loan Agreement\n...", filename: 'test-ila') }
  let(:supplemental_documents) { [] }
  let(:docusign_account_id) { SecureRandom.uuid }
  let(:webhook_url_token) { SecureRandom.uuid }
  let(:docusign_webhook_builder) { instance_double(Contracts::BuildDocusignWebhookUrl, url: "http://localhost:3002/api/loan/ipl/til/#{loan&.id}/#{webhook_url_token}?test=123", jwt_token: webhook_url_token) }
  let(:envelope_definition_payload) do
    {
      customFields: {
        textCustomFields: [{
          fieldId: 'unified_id',
          name: 'unified_id',
          show: 'true',
          value: '********'
        }]
      },
      documents: [{
        documentId: '2',
        fileExtension: 'pdf',
        name: 'CRB Installment Loan Agreement_version_26_ERICA_LAMBERT_f1ac8d93-e9ae-4e79-891a-01173685185c',
        documentBase64: 'JVBERi0xLjQKMSAwIG9iago8PAovVGl0bGUgKP7/KQovQ3JlYXRvciAo/v8pCi9Qcm9kdWNlciAo/v8AUQ...'
      }],
      emailSubject: 'Your Above Lending Loan Documents',
      eventNotification: {
        url: "https://dev.abovelending.com/api/loan/ipl/til/1dc35578-5587-4ec7-bb3e-cead55649b99/#{webhook_url_token}?webhookId=63a55028-b02d-47f3-a653-cbb1f5482bae&loanAgreementFilename=CRB+Installment+Loan+Agreement_version_26_ERICA_LAMBERT_dfcb4c06-7b87-4e34-807e-edc8bdc71911.pdf&loanAgreementTemplate=CRB_INSTALLMENT_LOAN_AGREEMENT&loanAgreementVersion=26&ipAddress=123.456.789.321"
      }
    }.as_json
  end
  let(:envelope_definition) { instance_double(DocuSign_eSign::EnvelopeDefinition, as_json: envelope_definition_payload) }
  let(:recipient_view_request) { instance_double(DocuSign_eSign::RecipientViewRequest) }

  let(:docusign_envelope_id) { SecureRandom.uuid }
  let(:envelope_creation_result) { instance_double(DocuSign_eSign::EnvelopeSummary, envelope_id: docusign_envelope_id) }
  let(:docusign_api_mock) { instance_double(Clients::DocusignApi, account_id: docusign_account_id, build_envelope_definition: envelope_definition, create_envelope: envelope_creation_result, build_recipient_view_request: recipient_view_request) }

  before do
    allow(Contracts::BuildDocusignWebhookUrl).to receive(:call).and_return(docusign_webhook_builder)
    allow(Clients::DocusignApi).to receive(:new).and_return(docusign_api_mock)
  end

  context 'when no loan is specified' do
    let(:loan) { nil }

    it 'raises an error' do
      expect { service.call }.to raise_error(ActiveModel::ValidationError, /Loan is required/i)
    end
  end

  context 'when no loan agreement document is specified' do
    let(:loan_agreement_document) { nil }

    it 'raises an error' do
      expect { service.call }.to raise_error(ActiveModel::ValidationError, /Loan agreement document is required/i)
    end
  end

  it 'returns itself to allow the relevant attributes to be extracted' do
    expect(service.call).to eq(service)
  end

  it 'generates a DocuSign webhook URL' do
    service.call

    expect(Contracts::BuildDocusignWebhookUrl).to have_received(:call).with(loan:, loan_agreement_document:, supplemental_documents:, webhook_id: service.docusign_webhook_id)
  end

  it 'triggers the creation of a new DocuSign envelope' do
    service.call
    expect(docusign_api_mock).to have_received(:build_envelope_definition) do |unified_id, signer, documents, webhook_url|
      expect(unified_id).to eq(loan.unified_id)

      expect(signer.email).to eq(borrower.email)
      expect(signer.name).to eq("#{borrower.first_name} #{borrower.last_name}")
      expect(signer.recipient_id).to eq(1)

      expect(documents.length).to eq(1)
      expect(documents.first.id).to eq(2) # ILA DocuSign document ID
      expect(documents.first.content).to eq(loan_agreement_document.content)
      expect(documents.first.name).to eq(loan_agreement_document.filename)

      expect(webhook_url).to match(docusign_webhook_builder.url)
    end
    expect(docusign_api_mock).to have_received(:create_envelope).with(envelope_definition)
  end

  it 'exposes the DocuSign webhook ID generated' do
    service.call
    expect(service.docusign_webhook_id).not_to be_nil
  end

  it 'exposes the ID of the resulting DocuSign envelope' do
    service.call
    expect(service.docusign_envelope_id).to eq(docusign_envelope_id)
  end

  context 'when the loan has the IPL product type' do
    before { loan.update!(product_type: Loan::IPL_LOAN_PRODUCT_TYPE) }

    it 'includes the state specific tabs configuration for the DocuSign envelope' do
      service.call
      expect(docusign_api_mock).to have_received(:build_envelope_definition) do |_unified_id, signer, _documents, _webhook_url|
        expect(signer.email).to eq(borrower.email)
        expect(signer.include_state_specific_tabs).to be true
      end
      expect(docusign_api_mock).to have_received(:create_envelope).with(envelope_definition)
    end
  end

  context 'when the loan has the UPL product type' do
    before { loan.update!(product_type: Loan::UPL_LOAN_PRODUCT_TYPE) }

    it 'includes the state specific tabs configuration for the DocuSign envelope' do
      service.call
      expect(docusign_api_mock).to have_received(:build_envelope_definition) do |_unified_id, signer, _documents, _webhook_url|
        expect(signer.email).to eq(borrower.email)
        expect(signer.include_state_specific_tabs).to be false
      end
      expect(docusign_api_mock).to have_received(:create_envelope).with(envelope_definition)
    end
  end

  context 'when the Maryland supplemental contract documents are included' do
    let(:credit_services_contract_template) { create(:doc_template, type: DocTemplate::TYPES[:CREDIT_SERVICES_CONTRACT_MARYLAND]) }
    let(:credit_services_contract) { build(:contract_document, template: credit_services_contract_template, content: "PDF-1.4%\nCredit Services Contract\n...", filename: 'test-credit-services-contract') }
    let(:notice_of_cancellation_template) { create(:doc_template, type: DocTemplate::TYPES[:NOTICE_OF_CANCELLATION_MARYLAND]) }
    let(:notice_of_cancellation) { build(:contract_document, template: notice_of_cancellation_template, template_type: notice_of_cancellation_template.type, content: "PDF-1.4%\nNotice of Cancellation\n...", filename: 'test-notice-of-cancellation') }
    let(:notice_of_cancellation2) { build(:contract_document, template: notice_of_cancellation_template, template_type: "#{notice_of_cancellation_template.type}_2", content: "PDF-1.4%\nNotice of Cancellation2\n...", filename: 'test-duplicate-notice-of-cancellation') }
    let(:supplemental_documents) do
      [
        credit_services_contract,
        notice_of_cancellation,
        notice_of_cancellation2
      ]
    end

    it 'includes the proper envelope documents' do
      service.call
      expect(docusign_api_mock).to have_received(:build_envelope_definition) do |_unified_id, _signer, documents, _webhook_url|
        expect(documents.length).to eq(4)

        credit_services_contract_doc = documents[0]
        expect(credit_services_contract_doc.id).to eq(3) # Credit Services Contract DocuSign document ID
        expect(credit_services_contract_doc.content).to eq(credit_services_contract.content)
        expect(credit_services_contract_doc.name).to eq(credit_services_contract.filename)

        notice_of_cancellation_doc = documents[1]
        expect(notice_of_cancellation_doc.id).to eq(4) # Notice of Cancellation DocuSign document ID
        expect(notice_of_cancellation_doc.content).to eq(notice_of_cancellation.content)
        expect(notice_of_cancellation_doc.name).to eq(notice_of_cancellation.filename)

        notice_of_cancellation2_doc = documents[2]
        expect(notice_of_cancellation2_doc.id).to eq(5) # Notice of Cancellation DocuSign document ID
        expect(notice_of_cancellation2_doc.content).to eq(notice_of_cancellation2.content)
        expect(notice_of_cancellation2_doc.name).to eq(notice_of_cancellation2.filename)

        installment_loan_agreement_doc = documents[3]
        expect(installment_loan_agreement_doc.id).to eq(2) # ILA DocuSign document ID
        expect(installment_loan_agreement_doc.content).to eq(loan_agreement_document.content)
        expect(installment_loan_agreement_doc.name).to eq(loan_agreement_document.filename)
      end
    end
  end
end
