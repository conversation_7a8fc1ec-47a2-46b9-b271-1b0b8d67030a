# frozen_string_literal: true

# == Schema Information
#
# Table name: loanpro_loans
#
#  id                    :uuid             not null, primary key
#  contract_date         :date
#  contract_generated_at :timestamptz
#  contract_started_at   :timestamptz
#  deleted_at            :timestamptz
#  loanpro_raw_response  :text
#  til_sign_date         :timestamptz
#  created_at            :timestamptz      not null
#  updated_at            :timestamptz
#  display_id            :string(255)
#  loan_id               :uuid             not null
#  loanpro_loan_id       :string(100)      not null
#  offer_id              :uuid
#
# Indexes
#
#  index_loanpro_loans_on_loan_id          (loan_id)
#  index_loanpro_loans_on_loanpro_loan_id  (loanpro_loan_id)
#  index_loanpro_loans_on_offer_id         (offer_id)
#
# Foreign Keys
#
#  FK-loan-loanpro-loan  (loan_id => loans.id)
#
require 'rails_helper'

RSpec.describe LoanproLoan, type: :model do
  include ActiveSupport::Testing::TimeHelpers

  describe '#expired?' do
    before { freeze_time }

    # Calculate created date in the past by:
    #  hours, minutes, and the expected results
    [
      [23, 55, true],
      [23, 56, true],
      [24, 0, true],
      [25, 57, true],
      [48, 0, true],
      [23, 59, true],
      # non-expired use cases
      [23, 54, false],
      [22, 59, false],
      [1, 0, false],
      [0, 1, false]
    ].each do |created_hours, created_minutes, expected_results|
      it "checks expiration for #{created_hours} hours and #{created_minutes} minutes ago and is #{expected_results}" do
        created_at = (created_hours.hours + created_minutes.minutes).ago
        loanpro_loan = build(:loanpro_loan, created_at:)

        expect(loanpro_loan.expired?).to eq(expected_results)
      end
    end

    describe '#temporary_loan_expired?' do
      before { freeze_time }

      it 'checks if loan expired after 24 hours' do
        created_at = 24.hours.ago
        loanpro_loan = build(:loanpro_loan, created_at:)

        expect(loanpro_loan.temporary_loan_expired?).to be_truthy
      end

      it 'checks if a loan is not expired before 24 hours' do
        created_at = (24.hours - 1.second).ago
        loanpro_loan = build(:loanpro_loan, created_at:)

        expect(loanpro_loan.temporary_loan_expired?).to be_falsey
      end
    end
  end

  describe '#valid_apr?' do
    let(:loan) { create(:loan) }
    let(:loanpro_loan) { create(:loanpro_loan, loan:) }

    def override_apr(value)
      loanpro_loan_data = JSON.parse(loanpro_loan.loanpro_raw_response)
      loanpro_loan.update!(loanpro_raw_response: loanpro_loan_data.deep_merge('LoanSetup' => { 'apr' => value }).to_json)
    end

    it 'is invalid if LoanPro failed to calculate the loan\'s APR' do
      override_apr(nil)
      expect(loanpro_loan.valid_apr?).to be_falsey
    end

    it 'is invalid if LoanPro calculate an APR value of 0% for the loan' do
      override_apr('0')
      expect(loanpro_loan.valid_apr?).to be_falsey
    end

    it 'is valid loans with an APR barely greater than 0%' do
      override_apr('0.01')
      expect(loanpro_loan.valid_apr?).to be_truthy
    end

    it 'is valid if the loan\'s APR exceeds the global 30% maximum' do
      override_apr('30.01')
      expect(loanpro_loan.valid_apr?).to be_falsey
    end

    it 'is valid if loans with an APR less than the 30% maximum' do
      override_apr('29.87')
      expect(loanpro_loan.valid_apr?).to be_truthy
    end

    { 'NY' => 25, 'PA' => 25, 'DC' => 24, 'MD' => 24 }.each do |state, max_apr|
      context "the borrower is from #{state}" do
        before do
          create(:borrower_additional_info, borrower: loan.borrower, state:)
        end

        it 'returns an error if the loan\'s APR exceeds the 25% maximum' do
          override_apr((max_apr + 0.01).to_s)
          expect(loanpro_loan.valid_apr?).to be_falsey
        end

        it 'permits loans with an APR equal to the 25% maximum' do
          override_apr(max_apr.to_d.to_s)
          expect(loanpro_loan.valid_apr?).to be_truthy
        end

        it 'permits loans with an APR less than the 25% maximum' do
          override_apr((max_apr - 0.01).to_s)
          expect(loanpro_loan.valid_apr?).to be_truthy
        end
      end
    end

    describe '#latest_signed' do
      it 'returns the latest signed loanpro loan' do
        median_loan = create(:loanpro_loan, created_at: 1.day.ago, til_sign_date: Time.current)
        oldest_loan = create(:loanpro_loan, created_at: 2.days.ago, til_sign_date: Time.current)
        latest_loan = create(:loanpro_loan, created_at: Time.current, til_sign_date: Time.current)

        expect(LoanproLoan.latest_signed).to eq([latest_loan, median_loan, oldest_loan])
        expect(LoanproLoan.latest_signed.first.signed_contract?).to be_truthy
      end
    end

    describe '#signed_contract?' do
      it 'is a signed contract' do
        loanpro_loan = create(:loanpro_loan, created_at: Time.current, til_sign_date: Time.current)

        expect(loanpro_loan.signed_contract?).to be_truthy
      end

      it 'is not a signed contract when deleted' do
        loanpro_loan = create(:loanpro_loan, deleted_at: Time.current, til_sign_date: Time.current)

        expect(loanpro_loan.signed_contract?).to be_falsey
      end

      it 'is not a signed contract when til_sign_date is nil' do
        loanpro_loan = create(:loanpro_loan, deleted_at: nil, til_sign_date: nil)

        expect(loanpro_loan.signed_contract?).to be_falsey
      end
    end
  end
end
