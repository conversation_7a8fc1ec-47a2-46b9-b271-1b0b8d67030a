# frozen_string_literal: true

# == Schema Information
#
# Table name: loan_inquiries
#
#  id                         :uuid             not null, primary key
#  application                :jsonb            not null
#  decline                    :jsonb
#  offers                     :jsonb
#  created_at                 :timestamptz      not null
#  updated_at                 :timestamptz
#  beyond_request_tracking_id :string(100)      not null
#  gds_request_id             :string(100)      not null
#  loan_id                    :uuid
#
# Indexes
#
#  index_loan_inquiries_on_loan_id                   (loan_id)
#  loan_inquiries_beyond_request_tracking_id_unique  (beyond_request_tracking_id) UNIQUE
#  loan_inquiries_gds_request_id_unique              (gds_request_id) UNIQUE
#
# Foreign Keys
#
#  FK-loan-loan_inquiries  (loan_id => loans.id)
#
require 'rails_helper'

module AboveLending
  RSpec.describe LoanInquiry, type: :model do
    describe 'associations' do
      it { should belong_to(:loan).optional }
    end

    describe '#expired?' do
      context 'when the offer expiration_date is in the past' do
        let(:loan_inquiry) { build(:loan_inquiry_expired) }

        it 'returns true' do
          expect(loan_inquiry.expired?).to be true
        end
      end

      context 'when the offer expiration_date is in the future' do
        let(:loan_inquiry) { build(:loan_inquiry) }

        it 'returns false' do
          expect(loan_inquiry.expired?).to be false
        end
      end

      context 'when offers is empty or nil' do
        let(:loan_inquiry) { build(:loan_inquiry, offers: nil) }

        it 'returns true' do
          expect(loan_inquiry.expired?).to be true
        end
      end
    end
  end
end
