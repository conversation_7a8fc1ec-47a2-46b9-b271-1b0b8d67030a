# frozen_string_literal: true

# == Schema Information
#
# Table name: loans
#
#  id                             :uuid             not null, primary key
#  adverse_action_sent            :timestamptz
#  affiliate_sharing              :boolean
#  amount                         :decimal(15, 2)   not null
#  anual_income                   :decimal(15, 2)
#  arix_onboarding_status         :string
#  bankruptcy_filed_date          :date
#  beyond_salesforce              :text
#  campaign                       :string(255)
#  code                           :string(100)
#  contract_signing_token         :uuid
#  crb_dry_run                    :boolean          default(FALSE), not null
#  credit_inquiries_last_6_months :integer
#  credit_score                   :decimal(8, 2)
#  credit_score_range             :string(20)
#  decision_reason_number         :string(100)
#  decline_reason_text            :text
#  decline_reasons                :jsonb
#  deleted_at                     :timestamptz
#  dti                            :decimal(16, 8)   default(0.0), not null
#  education_level                :string(100)
#  employment_industry            :string(150)
#  employment_pay_frecuency       :string(100)
#  employment_start_date          :string(50)
#  employment_status              :string(20)
#  exclude_from_allocation        :boolean          default(FALSE), not null
#  excluded_from_concentration_at :datetime
#  expiration_date                :timestamptz
#  housing_status                 :string(50)
#  last_paycheck_on               :date
#  monthly_deposit_amount         :decimal(15, 2)
#  monthly_housing_payment        :decimal(50, 2)
#  originating_party              :string(50)
#  product_type                   :string(100)      default("PPC")
#  program_duration_in_tmonths    :integer
#  purpose                        :string(50)       default("debt_consolidation")
#  requested_offers               :boolean          default(FALSE)
#  score_factor                   :text
#  screened_for_bankruptcy        :boolean          default(FALSE), not null
#  should_send_adverse_action     :boolean          default(FALSE), not null
#  source_type                    :string(10)
#  time_at_residence              :string(20)
#  verified_dti                   :decimal(16, 8)
#  verified_income                :decimal(15, 2)
#  verified_income_ratio          :decimal(16, 8)
#  created_at                     :timestamptz      not null
#  updated_at                     :timestamptz
#  affiliate_lead_id              :text
#  borrower_id                    :uuid
#  investor_id                    :uuid
#  loan_app_status_id             :integer
#  program_id                     :string(12)
#  request_id                     :string(100)
#  unified_id                     :string(255)
#
# Indexes
#
#  index_loans_on_contract_signing_token          (contract_signing_token) UNIQUE
#  index_loans_on_excluded_from_concentration_at  (excluded_from_concentration_at)
#  index_loans_on_investor_id                     (investor_id)
#  index_loans_on_loan_app_status_id              (loan_app_status_id)
#  index_loans_on_program_id                      (program_id)
#  loans_borrower_id_idx                          (borrower_id)
#  loans_code_uppercase_idx                       (upper((code)::text))
#  loans_product_type_idx                         (product_type)
#  loans_request_id_unique                        (request_id) UNIQUE
#  loans_unified_id_unique                        (unified_id) UNIQUE
#
# Foreign Keys
#
#  FK-borrower-loans   (borrower_id => borrowers.id)
#  FK-loan-app-status  (loan_app_status_id => loan_app_statuses.id)
#  FK-loan-owner       (investor_id => investors.id)
#
require 'rails_helper'

RSpec.describe Loan, type: :model do
  include ActiveSupport::Testing::TimeHelpers

  let(:above_lending_offers) { [build(:offer)] }
  let(:loan_app_status) { build(:loan_app_status) }
  let(:loan) { create(:loan, loan_app_status:, offers: above_lending_offers) }

  describe 'associations' do
    it { is_expected.to have_many(:plaid_reports).class_name('PlaidReport') }
    it { is_expected.to have_many(:ocrolus_reports).class_name('OcrolusReport') }
  end

  describe '#selected_offer' do
    let(:loan) { create(:loan) }
    let!(:selected_offer) { create(:offer, loan:, selected: true, created_at: 5.minutes.ago) }

    before do
      create(:offer, loan:, selected: false)
      create(:offer, loan:, selected: false)
      loan.reload
    end

    it { expect(loan.selected_offer).to eq(selected_offer) }

    it 'selects the latest selected offer' do
      latest_selected_offer = create(:offer, loan:, selected: true, created_at: 5.minutes.from_now)
      loan.reload

      expect(loan.selected_offer).to eq(latest_selected_offer)
    end
  end

  context 'lead' do
    let!(:lead) { create(:lead) }
    let!(:loan) { create(:loan, code: lead.code) }

    it 'fetches the lead' do
      expect(loan.lead).to eq(lead)
    end
  end

  context 'program_id_count' do
    let(:loan) { create(:loan, program_id:) }
    let(:program_id) { "BRP-#{SecureRandom.random_number(500) + 1_000}" }

    it 'returns 1 for a single loan' do
      expect(loan.program_id_count).to eq(1)
    end

    it 'returns 2 for two loans' do
      create(:loan, program_id:)

      expect(loan.program_id_count).to eq(2)
    end
  end

  context 'expirations' do
    before do
      allow(Time).to receive(:current).and_return(Time.new(2023, 7, 9))
    end

    context 'when a loan has an offer and has an expirable status' do
      let(:loan_app_status) { build(:loan_app_status, name: LoanAppStatus::POSSIBLE_EXPIRED_STATUSES.sample) }

      describe '#seconds_left' do
        it 'returns seconds left on latest offer' do
          expect(loan.seconds_left).to eq((above_lending_offers[0].expiration_date - Time.current).floor(6))
        end
      end

      describe '#hours_left' do
        it 'returns hours left on latest offer' do
          expect(loan.hours_left).to eq((above_lending_offers[0].expiration_date - Time.current) / 3600)
        end
      end
    end

    context 'when a loan has no offer or has a non-expirable status' do
      let(:loan_app_status) { build(:loan_app_status, name: LoanAppStatus::NON_EXPIRABLE_STATUS.sample) }

      describe '#seconds_left' do
        it 'returns 0' do
          expect(loan.seconds_left).to eq(0)
        end
      end

      describe '#hours_left' do
        it 'returns 0' do
          expect(loan.hours_left).to eq(0)
        end
      end
    end

    describe '#expired?' do
      context 'when loan app status is NO_OFFERS' do
        let(:loan_app_status) { build(:loan_app_status, name: 'NO_OFFERS') }

        it 'returns false' do
          expect(loan.expired?).to eq(false)
        end
      end

      context 'when loan app status is an expired status' do
        let(:loan_app_status) { build(:loan_app_status, name: LoanAppStatus::EXPIRED_STATUSES.sample) }

        it 'returns true' do
          expect(loan.expired?).to eq(true)
        end
      end

      context 'when loan app status is possibly expired and seconds left is less than 1' do
        let(:loan_app_status) { build(:loan_app_status, name: LoanAppStatus::POSSIBLE_EXPIRED_STATUSES.sample) }
        let(:above_lending_offers) { [build(:offer, expiration_date: Time.current)] }

        it 'returns true' do
          expect(loan.expired?).to eq(true)
        end
      end
    end
  end

  describe '#expire!' do
    before do
      allow(Rails.logger).to receive(:info)
      allow(Clients::GdsApi).to receive(:sync_status)
      allow(Loans::SyncStatusJob).to receive(:perform_async)
      allow(Loans::DeliverNoticeOfAdverseActionJob).to receive(:perform_async)
    end

    it 'logs the expiration action' do
      loan.expire!
      expect(Rails.logger).to have_received(:info).with("#{loan.class.name} - Marking loan #{loan.id} as expired")
    end

    it 'updates the loan with the expired status and sets relevant fields' do
      loan.expire!
      expect(loan.should_send_adverse_action).to eq(true)
      expect(loan.reload.loan_app_status_id).to eq(LoanAppStatus.id('EXPIRED'))
      expect(loan.decision_reason_number).to eq('0')
      expect(loan.decline_reason_text).to eq(Offer::EXPIRED_TEXT)
      expect(loan.decline_reasons).to eq([Offer::EXPIRED_TEXT])
      expect(loan.credit_score).to eq(0)
      expect(loan.score_factor).to eq('0')
    end

    it 'enqueues a job to sync the status with GDS' do
      loan.expire!
      expect(Loans::SyncStatusJob).to have_received(:perform_async).with(loan.id)
    end

    it 'enqueues a job to deliver notice of adverse action' do
      loan.expire!
      expect(Loans::DeliverNoticeOfAdverseActionJob).to have_received(:perform_async).with(loan.id)
    end
  end

  describe '#withdrawable' do
    context 'when the loan product type is eligible for withdrawal' do
      let(:loan) { create(:withdrawal_eligible_loan) }

      it 'returns true' do
        expect(loan.withdrawable?).to eq(true)
      end
    end

    context 'when the loan product type is not eligible for withdrawal' do
      let(:loan) { create(:loan, product_type: Lead::TYPES[:PPC]) }

      it 'returns false' do
        expect(loan.withdrawable?).to eq(false)
      end
    end
  end

  describe '#update_arix_funding_status_submission_status' do
    let(:submisson_status) { Loan::READY_FOR_ALLOCATION }
    it 'updates the arix_funding_status with the submission status' do
      loan.update!(arix_onboarding_status: submisson_status)
      expect(loan.arix_funding_status.submission_status).to eq(submisson_status)
    end
  end

  describe '#track_loan_status_history' do
    let!(:loan_app_status) { create(:loan_app_status, :pending) }
    let(:loan) { create(:loan, loan_app_status:, offers: above_lending_offers) }

    context 'when updating a loan status' do
      before { loan }

      let!(:new_loan_app_status) { create(:loan_app_status, :approved) }

      it 'creates a new loan status history' do
        expect do
          loan.update(loan_app_status: new_loan_app_status, updated_at: Time.now, bypass_loan_status_history: false)
        end.to change(LoanStatusHistory, :count).by(1)

        expect(LoanStatusHistory.last.loan_id).to eq(loan.id)
      end
    end
  end

  describe '.latest_active_for_borrower' do
    let!(:borrower) { create(:borrower) }
    let!(:active_loan) do
      create(:loan,
             borrower:,
             loan_app_status: create(:loan_app_status, :above_selected),
             created_at: 1.hour.ago)
    end
    let!(:latest_active_loan) do
      create(:loan,
             borrower:,
             loan_app_status: create(:loan_app_status, :pending))
    end
    let!(:inactive_loan) do
      create(:loan,
             borrower:,
             loan_app_status: create(:loan_app_status, :front_end_declined))
    end

    subject { described_class.latest_active_for_borrower(borrower) }

    it 'returns the latest active loan for borrower' do
      expect(subject).to eq([latest_active_loan, active_loan])
    end
  end

  describe '.current_for_borrower_by_email' do
    context 'when no borrower exists for the email' do
      it 'returns nil' do
        expect(described_class.current_for_borrower_by_email('<EMAIL>')).to be_nil
      end
    end

    context 'when a borrower exists with no loans for the email' do
      let(:borrower) { create(:borrower) }

      it 'returns nil' do
        expect(described_class.current_for_borrower_by_email(borrower.email)).to be_nil
      end
    end

    context 'when a borrower exists who has a single loan' do
      let(:borrower) { loan.borrower }
      let(:loan) { create(:loan) }

      it 'returns their only loan' do
        expect(described_class.current_for_borrower_by_email(borrower.email)).to eq(loan)
      end

      it 'preloads the loan\'s status' do
        loan = described_class.current_for_borrower_by_email(borrower.email)
        expect(loan.association(:loan_app_status)).to be_loaded
      end

      it 'preloads the loan\'s borrower' do
        loan = described_class.current_for_borrower_by_email(borrower.email)
        expect(loan.association(:borrower)).to be_loaded
      end

      it 'preloads the loan\'s tradeline details' do
        loan = described_class.current_for_borrower_by_email(borrower.email)
        expect(loan.association(:loan_tradeline_details)).to be_loaded
      end

      it 'preloads the loan\'s payment details' do
        loan = described_class.current_for_borrower_by_email(borrower.email)
        expect(loan.association(:loan_payment_detail)).to be_loaded
      end

      it 'preloads the loan\'s latest offer' do
        loan = described_class.current_for_borrower_by_email(borrower.email)
        expect(loan.association(:latest_offer)).to be_loaded
      end
    end

    context 'when a borrower has a multiple loans of the same type' do
      let(:product_type) { 'IPL' }
      let(:borrower) { most_recent_loan.borrower }
      let(:most_recent_loan) { create(:loan, product_type:, created_at: 1.hour.ago) }

      before do
        (2..5).each do |n|
          create(:loan, product_type:, created_at: n.days.ago)
        end
      end

      it 'returns the most recent loan' do
        expect(described_class.current_for_borrower_by_email(borrower.email)).to eq(most_recent_loan)
      end
    end

    context 'when a borrower has a multiple loans of different types' do
      let(:borrower) { create(:borrower) }

      it 'returns the IPL loan if one exists' do
        highest_priority_loan = create(:loan, borrower:, product_type: 'IPL', created_at: 2.days.ago)
        %w[IPL DM PPC OTHER].each { |product_type| create(:loan, borrower:, product_type:, created_at: 5.days.ago) }

        expect(described_class.current_for_borrower_by_email(borrower.email)).to eq(highest_priority_loan)
      end

      it 'returns the DM loan if no higher priority loan exists' do
        highest_priority_loan = create(:loan, borrower:, product_type: 'DM', created_at: 2.days.ago)
        %w[DM PPC OTHER].each { |product_type| create(:loan, borrower:, product_type:, created_at: 5.days.ago) }

        expect(described_class.current_for_borrower_by_email(borrower.email)).to eq(highest_priority_loan)
      end

      it 'returns the PPC loan if no higher priority loan exists' do
        highest_priority_loan = create(:loan, borrower:, product_type: 'PPC', created_at: 2.days.ago)
        %w[PPC OTHER].each { |product_type| create(:loan, borrower:, product_type:, created_at: 5.days.ago) }

        expect(described_class.current_for_borrower_by_email(borrower.email)).to eq(highest_priority_loan)
      end

      it 'returns the any other type of loan if no higher priority loan exists' do
        highest_priority_loan = create(:loan, borrower:, product_type: 'OTHER', created_at: 2.days.ago)
        %w[OTHER].each { |product_type| create(:loan, borrower:, product_type:, created_at: 5.days.ago) }

        expect(described_class.current_for_borrower_by_email(borrower.email)).to eq(highest_priority_loan)
      end
    end
  end

  describe '#current_for_borrower_by_id' do
    context 'when no borrower exists for the id' do
      it 'returns nil' do
        expect(described_class.current_for_borrower_by_id(SecureRandom.uuid)).to be_nil
      end
    end

    context 'when a borrower exists and has a single loan' do
      let(:borrower) { loan.borrower }
      let(:loan) { create(:loan) }

      it 'returns their only loan' do
        expect(described_class.current_for_borrower_by_id(borrower.id)).to eq(loan)
      end

      it 'preloads the expected associations' do
        loan = described_class.current_for_borrower_by_id(borrower.id)

        expect(loan.association(:loan_app_status)).to be_loaded
        expect(loan.association(:borrower)).to be_loaded
        expect(loan.association(:loan_tradeline_details)).to be_loaded
        expect(loan.association(:loan_tradeline_details)).to be_loaded
        expect(loan.association(:loan_payment_detail)).to be_loaded
        expect(loan.association(:latest_offer)).to be_loaded
      end
    end

    context 'when a borrower has a multiple loans of the same status' do
      let(:borrower) { most_recent_loan.borrower }
      let(:most_recent_loan) { create(:loan, :pending, created_at: 1.hour.ago) }

      before do
        (2..5).each do |n|
          create(:loan, :pending, borrower:, created_at: n.days.ago)
        end
      end

      it 'returns the most recent loan' do
        expect(described_class.current_for_borrower_by_id(borrower.id)).to eq(most_recent_loan)
      end
    end

    context 'when a borrower has both an active loan and a newer declined loan' do
      let(:borrower) { most_recent_loan.borrower }
      let(:most_recent_loan) { create(:loan, :approved, created_at: 1.hour.ago) }

      before do
        create(:loan, :front_end_declined, borrower:)
      end

      it 'returns the active loan' do
        expect(described_class.current_for_borrower_by_id(borrower.id)).to eq(most_recent_loan)
      end
    end
  end

  describe '#term_frequency' do
    subject(:loan) { create(:loan) }

    context 'when a selected offer with term_frequency exists' do
      it 'returns the selected offer term_frequency' do
        create(:offer, loan:, selected: true, term_frequency: 'monthly')
        create(:offer, loan:, selected: false)

        expect(loan.term_frequency).to eq('monthly')
      end
    end

    context 'when no selected offer exists on a UPL loan but an inquiry with offers is present' do
      subject(:loan) { create(:upl_loan) }

      it 'returns the payment frequency from the first loan inquiry offer' do
        create(:loan_inquiry, loan:, offers: [{ 'payment_frequency' => 'semi_monthly' }])

        expect(loan.term_frequency).to eq('semi_monthly')
      end
    end

    context 'when employment_pay_frecuency exists' do
      before { loan.update!(employment_pay_frecuency: employment_pay_frequency) }

      context 'and is weekly' do
        let(:employment_pay_frequency) { 'weekly' }

        it 'returns biweekly' do
          expect(loan.term_frequency).to eq('biweekly')
        end
      end

      context 'and is not weekly' do
        let(:employment_pay_frequency) { 'monthly' }

        it 'returns the employment pay frequency' do
          expect(loan.term_frequency).to eq('monthly')
        end
      end
    end

    context 'when a loan_payment_detail with beyond_payment_frequency exists' do
      it 'returns the beyond_payment_frequency' do
        expect(loan.term_frequency).to eq(nil)
      end
    end
  end

  describe 'after_commit :update_beyond_loan_status' do
    before do
      allow(Beyond::LoanStatusUpdateJob).to receive(:perform_async)
      travel_to('2024-06-04T12:12:00'.to_datetime)
    end

    context 'when an IPL loan is created or updated' do
      let(:loan) { build(:loan, product_type: 'IPL', bypass_beyond_status_update: false) }

      it 'enqueues the Beyond::LoanStatusUpdateJob in production' do
        environment_double = instance_double('ActiveSupport::EnvironmentInquirer', to_sym: :production, production?: true)
        expect(Rails).to receive(:env).at_least(:once).and_return(environment_double)

        loan.save

        update_args = {
          'loan_id' => loan.id,
          'loan_app_status' => loan.loan_app_status&.name,
          'updated_at' => loan.updated_at.to_s
        }
        expect(Beyond::LoanStatusUpdateJob).to have_received(:perform_async).with(update_args)
      end

      %i[test sandbox staging].each do |env|
        it "does not enqueue the Beyond::LoanStatusUpdateJob in the #{env} environment when override flag is disabled" do
          Flipper.disable(:execute_pre_production_beyond_status_update)

          environment_double = instance_double('ActiveSupport::EnvironmentInquirer', to_sym: env, production?: false)
          expect(Rails).to receive(:env).at_least(:once).and_return(environment_double)

          loan.save
          expect(Beyond::LoanStatusUpdateJob).not_to have_received(:perform_async)
        end

        it "enqueues the Beyond::LoanStatusUpdateJob in the #{env} environment when override flag is enabled" do
          Flipper.enable(:execute_pre_production_beyond_status_update)
          environment_double = instance_double('ActiveSupport::EnvironmentInquirer', to_sym: env, production?: false)
          expect(Rails).to receive(:env).at_least(:once).and_return(environment_double)

          loan.save

          update_args = {
            'loan_id' => loan.id,
            'loan_app_status' => loan.loan_app_status&.name,
            'updated_at' => loan.updated_at.to_s
          }
          expect(Beyond::LoanStatusUpdateJob).to have_received(:perform_async).with(update_args)
        end
      end
    end

    context 'when bypass_beyond_status_update is true' do
      let(:loan) { build(:loan, bypass_beyond_status_update: true) }

      it 'does not enqueue the Beyond::LoanStatusUpdateJob' do
        loan.save
        expect(Beyond::LoanStatusUpdateJob).not_to have_received(:perform_async)
      end
    end
  end

  describe 'after_commit :manage_dropoff_list' do
    let(:loan) { create(:ipl_loan, source_type: 'GDS', loan_app_status: LoanAppStatus.for('NEW')) }

    before do
      allow(Talkdesk::OrchestrateCallbackJob).to receive(:perform_async)
    end

    it 'delegates to an orchestrator' do
      loan.update!(loan_app_status: LoanAppStatus.for('APPROVED'))
      expect(Talkdesk::OrchestrateCallbackJob).to have_received(:perform_async).with(loan.id)
    end

    context 'when the loan has a non-IPL product type' do
      let(:loan) { create(:upl_loan, loan_app_status: LoanAppStatus.for('NEW')) }

      it 'does nothing' do
        loan.update!(loan_app_status: LoanAppStatus.for('APPROVED'))
        expect(Talkdesk::OrchestrateCallbackJob).not_to have_received(:perform_async)
      end
    end

    context 'when a loan update does not change the loan app status' do
      before do
        allow(loan).to receive(:manage_dropoff_list)
      end

      it 'does not trigger the manage_dropoff_list hook' do
        loan.update!(amount: loan.amount + 1)
        expect(loan).not_to have_received(:manage_dropoff_list)
      end
    end
  end

  describe 'declined_date' do
    let(:loan) { create(:loan, loan_app_status_id: LoanAppStatus.id(loan_app_status_name)) }

    context 'when the loan is not associated with a declined status' do
      let(:loan_app_status_name) { LoanAppStatus::ONGOING_LOAN_STATUSES.sample }

      before do
        create(:loan_status_history, loan:, new_status: "#{loan.product_type}_#{loan_app_status_name}", updated_at: 2.days.ago)
      end

      it 'returns nil' do
        expect(loan.declined_date).to be_nil
      end
    end

    context 'when the loan is associated with a declined status' do
      let(:loan_app_status_name) { LoanAppStatus::DECLINED_STATUSES.sample }
      let!(:loan_status_history) { create(:loan_status_history, loan:, new_status: "#{loan.product_type}_#{loan_app_status_name}", updated_at: 2.days.ago) }

      it 'returns the timestamp at which the loan was declined according to the LoanStatusHistory record' do
        expect(loan.declined_date).to eq(loan_status_history.reload.updated_at)
      end
    end
  end

  describe 'status methods' do
    let(:loan) do
      loan_app_status = LoanAppStatus.for('PENDING')

      create(:loan, loan_app_status:)
    end

    it 'returns true if the loan is equal to the given status' do
      expect(loan.pending?).to eq true
    end

    it 'returns false if the loan status is not equal to the given status ' do
      expect(loan.approved?).to eq false
    end

    it 'raises a method not found error when that status does not exist' do
      expect { loan.a_fake_status? }.to raise_error(NoMethodError)
    end
  end
end
