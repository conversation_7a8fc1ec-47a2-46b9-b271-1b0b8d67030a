# frozen_string_literal: true

# == Schema Information
#
# Table name: borrower_aditional_info
#
#  id                    :uuid             not null, primary key
#  address_apt           :string(100)
#  address_street        :string(100)
#  city                  :string(50)
#  deleted_at            :timestamptz
#  married               :boolean          not null
#  phone_number          :string(10)
#  spouse_address_apt    :string
#  spouse_address_street :string
#  spouse_city           :string
#  spouse_first_name     :string
#  spouse_last_name      :string
#  spouse_state          :string
#  spouse_zip_code       :string
#  state                 :string(2)
#  zip_code              :string(5)
#  created_at            :timestamptz      not null
#  updated_at            :timestamptz
#  borrower_id           :uuid
#  loan_id               :uuid
#
# Indexes
#
#  index_borrower_aditional_info_on_borrower_id  (borrower_id)
#  index_borrower_aditional_info_on_loan_id      (loan_id)
#
# Foreign Keys
#
#  FK-borrower-aditional-info       (borrower_id => borrowers.id)
#  FK-loan-borrower-aditional-info  (loan_id => loans.id)
#
require 'rails_helper'

RSpec.describe BorrowerAdditionalInfo, type: :model do
  subject { build(:borrower_additional_info) }

  describe 'associations' do
    it { is_expected.to belong_to(:borrower).class_name('<PERSON>rrow<PERSON>') }
    it { is_expected.to belong_to(:loan).class_name('::<PERSON>an') }
  end
end
