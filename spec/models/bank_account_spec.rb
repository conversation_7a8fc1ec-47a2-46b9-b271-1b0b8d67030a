# frozen_string_literal: true

# == Schema Information
#
# Table name: bank_accounts
#
#  id                       :uuid             not null, primary key
#  account_number           :string(100)      not null
#  account_type             :string(100)
#  allow_verify             :boolean          default(TRUE)
#  bank                     :string(100)      not null
#  deleted_at               :timestamptz
#  enabled                  :boolean          default(TRUE)
#  fund_transfer_authorize  :boolean
#  holder_firstname         :string(100)
#  holder_lastname          :string(100)
#  last_four_account_number :string(4)        not null
#  last_four_routing_number :string(4)        not null
#  plaid_access_token       :string(100)
#  routing_number           :string(100)      not null
#  username                 :string(100)
#  created_at               :timestamptz      not null
#  updated_at               :timestamptz
#  borrower_id              :uuid             not null
#  loan_id                  :uuid             not null
#  plaid_account_id         :string(100)
#  plaid_item_id            :string(100)
#
# Indexes
#
#  bank_accounts_borrower_id_loan_id_routing_number_account_number  (borrower_id,loan_id,routing_number,account_number) UNIQUE
#  index_bank_accounts_on_loan_id                                   (loan_id)
#  index_bank_accounts_on_plaid_item_id                             (plaid_item_id)
#
# Foreign Keys
#
#  FK-borrower-bank-accounts  (borrower_id => borrowers.id)
#  FK-loan-bank-accounts      (loan_id => loans.id)
#
require 'rails_helper'

RSpec.describe BankAccount, type: :model do
  subject { build(:bank_account) }

  describe 'associations' do
    it { is_expected.to belong_to(:borrower).class_name('Borrower') }
    it { is_expected.to belong_to(:loan).class_name('::Loan') }
  end

  describe '.account_name' do
    let(:bank_account) { build(:bank_account, holder_firstname: 'John', holder_lastname: 'Smith') }

    it 'return accound holder full name' do
      expect(bank_account.account_name).to eq('John Smith')
    end
  end
end
