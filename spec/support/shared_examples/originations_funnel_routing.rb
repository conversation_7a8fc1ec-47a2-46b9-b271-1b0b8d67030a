# frozen_string_literal: true

shared_examples 'an enforced originations funnel route' do |current_path:, unauthenticated: true|
  let(:code) { 'abc123' }
  let(:service_entity) { 'bf' }

  let!(:lead) { create(:lead, code:) }
  let!(:landing_lead) { create(:landing_lead, lead_code: code) }
  let!(:borrower) { create(:borrower, email: landing_lead.email) }
  let!(:loan) { create(:loan, borrower:, code:, product_type: Loan::IPL_LOAN_PRODUCT_TYPE) }

  def expect_route(current_path:, expected_route:, code:, service_entity:)
    if expected_route == current_path
      expect(response).to be_ok
    else
      expect(response).to redirect_to(send(expected_route, { offer: code, s: service_entity }))
    end
  end

  describe "routing from #{current_path}" do
    if unauthenticated
      context 'while unauthenticated' do
        before do
          mock_session!({})
          allow(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_return(nil)
        end

        it "should route to signin with redirect to #{current_path}" do
          get send(current_path)

          expect(response).to redirect_to(signin_borrowers_path(redirect: send(current_path)))
        end
      end
    end

    context 'while authenticated' do
      before do
        mock_session!(code:, service_entity:, loan_id: loan.id, borrower_id: borrower.id)

        mock_trustpilot_summary_response
        mock_trustpilot_reviews_response

        allow(Users::CheckAccountActivation).to receive(:call).and_return(false)
        allow(Plaid::GenerateLinkToken).to receive(:call)
      end

      it 'routes to offer select when while awaiting offers' do
        loan.update!(
          loan_app_status: LoanAppStatus.for(LoanAppStatus::ADD_INFO_COMPLETE_STATUS),
          requested_offers: true
        )

        get send(current_path, offer: code, s: service_entity)

        expect_route(
          current_path:,
          expected_route: :select_offer_loan_applications_path,
          code:,
          service_entity:
        )
      end

      it "routes to credit freeze when credit freeze is active and application is in status #{LoanAppStatus::BASIC_INFO_COMPLETE_STATUS}" do
        loan.update!(loan_app_status: LoanAppStatus.for(LoanAppStatus::BASIC_INFO_COMPLETE_STATUS))
        create(:loan_detail, loan:)
        loan.loan_detail.update!(credit_freeze_active: true)

        get send(current_path, offer: code, s: service_entity)

        expect_route(
          current_path:,
          expected_route: :credit_freeze_loan_applications_path,
          code:,
          service_entity:
        )
      end

      it "does not redirect a route to manual_bank_accounts_path for status #{LoanAppStatus::PENDING_STATUS}" do
        loan.update!(loan_app_status: LoanAppStatus.for(LoanAppStatus::PENDING_STATUS))

        get send(:manual_bank_accounts_path)

        expect(response).to be_ok
      end

      it "does not redirect a route to bank_accounts_path for status #{LoanAppStatus::PENDING_STATUS}" do
        loan.update!(loan_app_status: LoanAppStatus.for(LoanAppStatus::PENDING_STATUS))

        get send(:bank_accounts_path)

        expect(response).to be_ok
      end

      it "does not redirect a route to todos_path for status #{LoanAppStatus::APPROVED_STATUS} without contract" do
        loan.update!(loan_app_status: LoanAppStatus.for(LoanAppStatus::APPROVED_STATUS))
        loan.loanpro_loan.destroy if loan.loanpro_loan.present?

        get send(:todos_path)

        expect(response).to be_ok
      end

      it "does not redirect a route to contracts_path for status #{LoanAppStatus::APPROVED_STATUS} with contract" do
        loan.update!(loan_app_status: LoanAppStatus.for(LoanAppStatus::APPROVED_STATUS))
        create(:loanpro_loan, loan:)

        get send(:contracts_path)

        expect(response).to be_ok
      end

      it "does not redirect a route to servicing_dashboard_index_path for status #{LoanAppStatus::ONBOARDED_STATUS}" do
        allow(Loanpro::DashboardDetails).to receive(:call) do
          Clients::DashServicingApi::DashboardDetails.new({ next_payment_amount: 1 })
        end
        allow(Loanpro::PaymentProfiles).to receive(:call) do
          Clients::DashServicingApi::PaymentProfiles.new({})
        end
        allow(Loanpro::UpcomingPayments).to receive(:call) do
          Clients::DashServicingApi::UpcomingPayments.new({})
        end
        allow(Loanpro::PaymentHistory).to receive(:call) do
          Clients::DashServicingApi::PaymentHistory.new({})
        end
        allow(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_return(nil)

        loan.update!(loan_app_status: LoanAppStatus.for(LoanAppStatus::ONBOARDED_STATUS), investor: create(:investor))

        get send(:servicing_dashboard_index_path)

        expect(response).to be_ok
      end

      OriginationsFunnelRoutable::PATH_BY_STATUS.except(LoanAppStatus::PENDING_STATUS, LoanAppStatus::APPROVED_STATUS, LoanAppStatus::ONBOARDED_STATUS).each do |status, path|
        if path == current_path
          it "does not redirect a route to #{path} for status #{status}" do
            loan.update!(loan_app_status: LoanAppStatus.for(status))

            get send(current_path)

            expect(response).to be_ok
          end
        else
          it "should route #{status} to #{path}" do
            loan.update!(loan_app_status: LoanAppStatus.for(status))

            get send(current_path)

            expect(response).to redirect_to(send(path, { offer: code, s: service_entity }))
          end
        end
      end
    end
  end
end
